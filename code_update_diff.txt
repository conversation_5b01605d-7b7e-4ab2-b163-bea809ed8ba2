diff --git a/.gitlab-ci.yml b/.gitlab-ci.yml
index 9ede2154..c1c25dac 100644
--- a/.gitlab-ci.yml
+++ b/.gitlab-ci.yml
@@ -6,6 +6,12 @@ variables:
   DEPLOY_APP_DOCKER_IMAGE: "reg.firstshare.cn/app/fs-k8s-app-manager:${CI_COMMIT_REF_NAME}"
   DEPLOY_APP_DOCKER_IMAGE_FONESHARE: "reg.foneshare.cn/app/fs-k8s-app-manager:${CI_COMMIT_REF_NAME}"
 
+workflow:
+  rules:
+    - if: '$CI_COMMIT_REF_NAME == "master"'
+      when: always
+
+
 build_image:
   stage: build
   when: manual
diff --git a/back-end/config/oncall.go b/back-end/config/oncall.go
index a4581113..ad1f7a4a 100644
--- a/back-end/config/oncall.go
+++ b/back-end/config/oncall.go
@@ -22,6 +22,7 @@ type OnCallConfig struct {
 type Alert struct {
 	Name    string       `json:"name"`
 	Desc    string       `json:"desc"`
+	DryRun  bool         `json:"dryRun"`
 	AppList ResourceList `json:"appList"`
 }
 
@@ -47,6 +48,7 @@ func (o OnCallConfig) GetAlert(alertName string) Alert {
 	}
 	return Alert{
 		Name:    alertName,
+		DryRun:  true,
 		Desc:    "",
 		AppList: ResourceList{},
 	}
diff --git a/back-end/web/openapi/oncall.go b/back-end/web/openapi/oncall.go
index 1e755f30..c27b67fa 100644
--- a/back-end/web/openapi/oncall.go
+++ b/back-end/web/openapi/oncall.go
@@ -126,8 +126,8 @@ func OncallWebhook(c *gin.Context) {
 	if operate == "ScaleUp" {
 		data, err = scaleUp(p)
 	} else if operate == "ThreadDump" {
-		data, err = ThreadDump(p)
-	} else if operate == "podDeregister" {
+		data, err = threadDump(p)
+	} else if operate == "PodDeregister" {
 		data, err = podDeregister(p)
 	} else if operate == "NodeDrain" {
 		data, err = nodeDrain(p)
@@ -163,19 +163,40 @@ func scaleUp(p OncallWebhookParam) (interface{}, error) {
 		return nil, fmt.Errorf("skipped, app is not in alert whitelist")
 	}
 
-	if scaleUpIsLocked(cluster, namespace, app) {
-		return nil, fmt.Errorf("skipped, app scale up is locked because of has been scaled up recently")
+	dryRun := config.GetOncallConfig().GetAlert(p.AlertName).DryRun
+	beforeReplicas, afterReplicas, err := scaleUpExecute(cluster, namespace, app, addReplica, dryRun)
+	if err != nil {
+		return nil, err
 	}
-	//todo: 如果服务处于发版或重启过程中（创建时间小于15分钟）则跳过扩容
 
-	dep, err := k8s_service.GetDeploymentDTO(cluster, p.Namespace, app)
+	if dryRun {
+		sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ScaleUp(DryRun)",
+			fmt.Sprintf("服务 %s 因 %s 告警触发了自动扩容，副本数：%d → %d", app, p.AlertName, beforeReplicas, afterReplicas))
+		return nil, nil
+	}
+
+	//该日志给 scaleDownOncallScaledApps 定时任务使用。参考：jobs.go 下的 scaleDownOncallScaledApps
+	log_service.Create(p.AlertName, "oncall-scale-up", fmt.Sprintf("%s/%s/%s", cluster, namespace, app), "")
+
+	sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ScaleUp",
+		fmt.Sprintf("服务 %s 因 %s 告警触发了自动扩容，副本数：%d → %d", app, p.AlertName, beforeReplicas, afterReplicas))
+
+	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, app),
+		fmt.Sprintf("【告警自愈】服务扩容，副本数: %d → %d", beforeReplicas, afterReplicas))
+
+	return nil, nil
+}
+
+func scaleUpExecute(cluster, namespace, app string, addReplica int32, dryRun bool) (beforeReplicas, afterReplicas int32, err error) {
+	if scaleUpIsLocked(cluster, namespace, app) {
+		return -1, -1, fmt.Errorf("skipped, app scale up is locked because of has been scaled up recently")
+	}
+	dep, err := k8s_service.GetDeploymentDTO(cluster, namespace, app)
 	if err != nil {
-		return nil, err
+		return -1, -1, err
 	}
 	if dep.Replicas == 0 {
-		//todo: debug日志，等cluster问题解决后删除
-		log_service.Create(p.AlertName, "OnCall-服务扩容-Debug", app, p)
-		return nil, fmt.Errorf("skipped, current replicas is 0")
+		return -1, -1, fmt.Errorf("skipped, current replicas is 0")
 
 	}
 
@@ -184,26 +205,16 @@ func scaleUp(p OncallWebhookParam) (interface{}, error) {
 		allowedMaxReplicas = clu.ScaleMaxReplicas
 	}
 	if dep.Replicas >= allowedMaxReplicas {
-		return nil, fmt.Errorf("skipped, current replicas is greater than allowed max replicas %d", allowedMaxReplicas)
+		return -1, -1, fmt.Errorf("skipped, current replicas is greater than allowed max replicas %d", allowedMaxReplicas)
 	}
 	newReplicas := dep.Replicas + addReplica
 
-	err = kubectl.Scale(cluster, namespace, app, newReplicas)
-	if err != nil {
-		return nil, err
+	if dryRun {
+		return dep.Replicas, newReplicas, nil
 	}
-
-	//该日志给 scaleDownOncallScaledApps 定时任务使用。参考：jobs.go 下的 scaleDownOncallScaledApps
-	log_service.Create(p.AlertName, "oncall-scale-up", fmt.Sprintf("%s/%s/%s", cluster, namespace, app), "")
 	scaleUpLock(cluster, namespace, app, 15*time.Minute)
-
-	sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ScaleUp",
-		fmt.Sprintf("服务 %s 因 %s 告警触发了自动扩容，副本数：%d → %d", app, p.AlertName, dep.Replicas, newReplicas))
-
-	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, app),
-		fmt.Sprintf("【告警自愈】服务扩容，副本数: %d → %d", dep.Replicas, newReplicas))
-
-	return nil, nil
+	err = kubectl.Scale(cluster, namespace, app, newReplicas)
+	return dep.Replicas, newReplicas, err
 }
 
 func podDeregister(p OncallWebhookParam) (interface{}, error) {
@@ -215,23 +226,19 @@ func podDeregister(p OncallWebhookParam) (interface{}, error) {
 		return nil, fmt.Errorf("skipped, app is not in alert whitelist")
 	}
 
-	isPodResource := false
-	if pod, err2 := k8s_service.PodDetail(cluster, namespace, p.ResourceID); pod != nil && err2 == nil {
-		isPodResource = true
-		//对于启动不久的pod，关闭摘除。避免Pod启动阶段被摘除
-		if time.Now().Unix()-pod.CreationTimestamp.Unix() < 900 {
-			return nil, fmt.Errorf("skipped, pod is created less than 15 minutes")
-		}
+	dryRun := config.GetOncallConfig().GetAlert(p.AlertName).DryRun
+	err := podDeregisterExecute(cluster, namespace, p.ResourceID, dryRun)
+	if err != nil {
+		return nil, err
 	}
-	if !isPodResource {
-		return nil, fmt.Errorf("skipped, resource is not pod")
+	if dryRun {
+		sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "Deregister(DryRun)",
+			fmt.Sprintf("Pod %s 因 %s 告警触发了自动摘除", p.ResourceID, p.AlertName))
+		return nil, nil
 	}
-	_ = kubectl.DeregisterService(cluster, namespace, p.ResourceID)
-	_ = k8s_service.PodDeregister(cluster, namespace, p.ResourceID)
 
 	sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "Deregister",
 		fmt.Sprintf("Pod %s 因 %s 告警触发了自动摘除", p.ResourceID, p.AlertName))
-
 	//待改进： 记录摘除状态
 	event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, namespace, app),
 		fmt.Sprintf("【告警自愈】Pod摘除，名称: %s", p.ResourceID))
@@ -239,14 +246,56 @@ func podDeregister(p OncallWebhookParam) (interface{}, error) {
 	return nil, nil
 }
 
+func podDeregisterExecute(cluster, namespace, pod string, dryRun bool) error {
+	if podDetail, err := k8s_service.PodDetail(cluster, namespace, pod); podDetail != nil && err == nil {
+		//对于启动不久的pod，关闭摘除。避免Pod启动阶段被摘除
+		if time.Now().Unix()-podDetail.CreationTimestamp.Unix() < 900 {
+			return fmt.Errorf("skipped, pod is created less than 15 minutes")
+		}
+	} else {
+		return fmt.Errorf("skipped, resource is not pod, path: %s/%s/%s", cluster, namespace, pod)
+	}
+	if dryRun {
+		return nil
+	}
+	err := kubectl.DeregisterService(cluster, namespace, pod)
+	if err != nil {
+		return err
+	}
+	err = k8s_service.PodDeregister(cluster, namespace, pod)
+	return err
+}
+
 func nodeDrain(p OncallWebhookParam) (interface{}, error) {
 	cluster := p.AlertEnv
 	node := p.ResourceID
 	if strings.EqualFold(p.AlertEnv, "k8s1") {
 		node = ipToVlnxName(node)
 	}
+	alert := config.GetOncallConfig().GetAlert(p.AlertName)
+	data, err := nodeDrainExecute(cluster, node, alert)
+	if err != nil {
+		return nil, err
+	}
+	for _, item := range data {
+		if strings.EqualFold(item["status"], "success") {
+			selfHealingType := "Eviction"
+			if alert.DryRun {
+				selfHealingType = "Eviction(DryRun)"
+			}
+			sendSelfHealingEvent(cluster, item["namespace"], "app", item["app"], alert.Name, selfHealingType,
+				fmt.Sprintf("Pod %s 因 %s 告警触发了自动驱逐", item["pod"], alert.Name))
+			if !alert.DryRun {
+				event_service.Create(alert.Name, event_service.BuildAppKey(cluster, item["namespace"], item["app"]),
+					fmt.Sprintf("【告警自愈】Pod驱逐，名称: %s", item["pod"]))
+			}
+		}
+	}
+	return data, nil
+}
 
-	podList, err := k8s.ListPodByNode(p.AlertEnv, node)
+func nodeDrainExecute(cluster, node string, alert config.Alert) ([]map[string]string, error) {
+	podList, err := k8s.ListPodByNode(cluster, node)
 	if err != nil {
 		return nil, err
 	}
@@ -254,7 +303,7 @@ func nodeDrain(p OncallWebhookParam) (interface{}, error) {
 	drainPodList := make([]corev1.Pod, 0, 5)
 	for _, pod := range podList.Items {
 		app := k8s_util.GetAppName(pod.Name)
-		if config.GetOncallConfig().GetAlert(p.AlertName).AppAllow(cluster, pod.Namespace, app) {
+		if alert.AppAllow(cluster, pod.Namespace, app) {
 			drainPodList = append(drainPodList, pod)
 		}
 	}
@@ -264,29 +313,26 @@ func nodeDrain(p OncallWebhookParam) (interface{}, error) {
 	}
 
 	if len(drainPodList) > 0 {
-		if nodeDrainIsLocked(p.AlertEnv, node) {
+		if nodeDrainIsLocked(cluster, node) {
 			return nil, fmt.Errorf("skipped, node drain is locked")
 		}
-		nodeDrainLock(p.AlertEnv, node, 10*time.Minute)
+		nodeDrainLock(cluster, node, 10*time.Minute)
 	}
 
-	data := make([]map[string]interface{}, 0, len(drainPodList))
+	data := make([]map[string]string, 0, len(drainPodList))
 	for _, pod := range drainPodList {
 		status := "skipped"
-		if e := k8s_service.PodDelete(p.AlertEnv, pod.Namespace, pod.Name); e != nil {
+		app := k8s_util.GetAppName(pod.Name)
+		if e := k8s_service.PodDelete(cluster, pod.Namespace, pod.Name); e != nil {
 			status = "delete failed, err: " + e.Error()
 		} else {
-			status = "delete success"
-			app := k8s_util.GetAppName(pod.Name)
-			event_service.Create(p.AlertName, event_service.BuildAppKey(cluster, pod.Namespace, app),
-				fmt.Sprintf("【告警自愈】Pod驱逐，名称: %s", pod.Name))
-			sendSelfHealingEvent(p.AlertEnv, pod.Namespace, "app", app, p.AlertName, "Eviction",
-				fmt.Sprintf("Pod %s 因 %s 告警触发了自动驱逐", pod.Name, p.AlertName))
+			status = "success"
 		}
-		data = append(data, map[string]interface{}{
-			"cluster":      p.AlertEnv,
+		data = append(data, map[string]string{
+			"cluster":      cluster,
 			"namespace":    pod.Namespace,
 			"node":         node,
+			"app":          app,
 			"pod":          pod.Name,
 			"server_level": pod.Annotations["fxiaoke.com/server-level"],
 			"status":       status,
@@ -295,7 +341,7 @@ func nodeDrain(p OncallWebhookParam) (interface{}, error) {
 	return data, nil
 }
 
-func ThreadDump(p OncallWebhookParam) (interface{}, error) {
+func threadDump(p OncallWebhookParam) (interface{}, error) {
 	cluster := p.AlertEnv
 	namespace := p.Namespace
 	app := p.ResourceName
@@ -311,6 +357,12 @@ func ThreadDump(p OncallWebhookParam) (interface{}, error) {
 		return nil, err
 	}
 
+	if config.GetOncallConfig().GetAlert(p.AlertName).DryRun {
+		sendSelfHealingEvent(cluster, namespace, "app", app, p.AlertName, "ThreadDump(DryRun)",
+			fmt.Sprintf("Pod %s 因 %s 告警触发了线程Dump，文件地址：%s", pod, p.AlertName, "--"))
+		return "filepath: --", nil
+	}
+
 	dumpFile, err := k8s_service.ThreadDump(cluster, namespace, pod, false)
 	if err != nil {
 		return nil, err
diff --git a/k8s-manifest/templates/configmap-firstshare.yml b/k8s-manifest/templates/configmap-firstshare.yml
index a79f5778..6092954b 100644
--- a/k8s-manifest/templates/configmap-firstshare.yml
+++ b/k8s-manifest/templates/configmap-firstshare.yml
@@ -138,6 +138,7 @@ data:
         {
           "name": "pod-restart-oom-webhook",
           "desc": "OOM次数-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-paas-process-orch-task",
@@ -198,6 +199,7 @@ data:
         {
           "name": "jvm-gc-more",
           "desc": "GC时长-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -206,6 +208,7 @@ data:
         {
           "name": "jvm-old-gc-more",
           "desc": "GC次数-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -214,6 +217,7 @@ data:
         {
           "name": "log-error-webhook",
           "desc": "服务错误-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -222,6 +226,7 @@ data:
         {
           "name": "log-apibus-error-service-webhook",
           "desc": "服务提供者apibus错误-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -230,6 +235,7 @@ data:
         {
           "name": "log-cep-error-service-webhook",
           "desc": "服务cep错误-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -238,6 +244,7 @@ data:
         {
           "name": "pod-liveness-failed-webhook",
           "desc": "存活探针失败-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -246,6 +253,7 @@ data:
         {
           "name": "jvm-gc-more",
           "desc": "GC耗时-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -254,6 +262,7 @@ data:
         {
           "name": "jvm-old-gc-more",
           "desc": "GC次数-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -262,6 +271,7 @@ data:
         {
           "name": "k8s-node-mem-usage-webhook",
           "desc": "宿主机内存使用率-自愈-驱逐",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-file-datax-stats",
@@ -519,18 +529,12 @@ data:
               "*/*/fs-k8s-tomcat-test"
             ],
             "blacklist": []
-          },
-          "nodeList": {
-            "whitelist": [
-              "k8s0/*",
-              "k8s1/*"
-            ],
-            "blacklist": []
           }
         },
         {
           "name": "node-load1-k8s-webhook",
           "desc": "宿主机CPU负载-自愈-驱逐",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-file-datax-stats",
@@ -788,18 +792,12 @@ data:
               "*/*/fs-k8s-tomcat-test"
             ],
             "blacklist": []
-          },
-          "nodeList": {
-            "whitelist": [
-              "k8s0/*",
-              "k8s1/*"
-            ],
-            "blacklist": []
           }
         },
         {
           "name": "app-available-replicas3-webhook",
           "desc": "服务可用率-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-paas-process-orch-task",
@@ -1014,6 +1012,7 @@ data:
         {
           "name": "jvm-tomcat-http-blocked",
           "desc": "Tomcat的http请求排队-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-paas-process-orch-task",
@@ -1074,6 +1073,7 @@ data:
         {
           "name": "k8s-event-pod-unhealthy",
           "desc": "健康检测失败-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -1082,6 +1082,7 @@ data:
         {
           "name": "pod-cpu-throttled",
           "desc": "CPU受限-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-paas-process-orch-task",
@@ -1142,6 +1143,7 @@ data:
         {
           "name": "jvm-thread-blocked",
           "desc": "Pod-JVM线程阻塞",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/*"
diff --git a/k8s-manifest/templates/configmap-foneshare.yml b/k8s-manifest/templates/configmap-foneshare.yml
index d15e0142..a498ae93 100644
--- a/k8s-manifest/templates/configmap-foneshare.yml
+++ b/k8s-manifest/templates/configmap-foneshare.yml
@@ -143,6 +143,7 @@ data:
         {
           "name": "pod-restart-oom-webhook",
           "desc": "OOM次数-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-paas-process-orch-task",
@@ -203,6 +204,7 @@ data:
         {
           "name": "jvm-gc-more",
           "desc": "GC时长-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -211,6 +213,7 @@ data:
         {
           "name": "jvm-old-gc-more",
           "desc": "GC次数-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -219,6 +222,7 @@ data:
         {
           "name": "log-error-webhook",
           "desc": "服务错误-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -227,6 +231,7 @@ data:
         {
           "name": "log-apibus-error-service-webhook",
           "desc": "服务提供者apibus错误-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -235,6 +240,7 @@ data:
         {
           "name": "log-cep-error-service-webhook",
           "desc": "服务cep错误-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -243,6 +249,7 @@ data:
         {
           "name": "pod-liveness-failed-webhook",
           "desc": "存活探针失败-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -251,6 +258,7 @@ data:
         {
           "name": "jvm-gc-more",
           "desc": "GC耗时-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -259,6 +267,7 @@ data:
         {
           "name": "jvm-old-gc-more",
           "desc": "GC次数-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -267,6 +276,7 @@ data:
         {
           "name": "k8s-node-mem-usage-webhook",
           "desc": "宿主机内存使用率-自愈-驱逐",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-file-datax-stats",
@@ -524,18 +534,12 @@ data:
               "*/*/fs-k8s-tomcat-test"
             ],
             "blacklist": []
-          },
-          "nodeList": {
-            "whitelist": [
-              "k8s0/*",
-              "k8s1/*"
-            ],
-            "blacklist": []
           }
         },
         {
           "name": "node-load1-k8s-webhook",
           "desc": "宿主机CPU负载-自愈-驱逐",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-file-datax-stats",
@@ -793,18 +797,12 @@ data:
               "*/*/fs-k8s-tomcat-test"
             ],
             "blacklist": []
-          },
-          "nodeList": {
-            "whitelist": [
-              "k8s0/*",
-              "k8s1/*"
-            ],
-            "blacklist": []
           }
         },
         {
           "name": "app-available-replicas3-webhook",
           "desc": "服务可用率-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [
             ],
@@ -814,6 +812,7 @@ data:
         {
           "name": "jvm-tomcat-http-blocked",
           "desc": "Tomcat的http请求排队-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/fs-paas-process-orch-task",
@@ -874,6 +873,7 @@ data:
         {
           "name": "k8s-event-pod-unhealthy",
           "desc": "健康检测失败-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [],
             "blacklist": []
@@ -882,6 +882,7 @@ data:
         {
           "name": "pod-cpu-throttled",
           "desc": "CPU受限-自愈-自动扩容",
+          "dryRun": false,
           "appList": {
             "whitelist": [
             ],
@@ -891,6 +892,7 @@ data:
         {
           "name": "jvm-thread-blocked",
           "desc": "Pod-JVM线程阻塞",
+          "dryRun": false,
           "appList": {
             "whitelist": [
               "*/*/*"
