apiVersion: v1
kind: ConfigMap
metadata:
  name: {{DEPLOY_APP_NAME}}-config
  namespace: {{DEPLOY_K8S_NAMESPACE}}
  annotations:
    fxiaoke.com/managed-by: gitlab-ci
data:
  config.json: |-
    {
      "app": {
        "runMode": "release",
        "httpPort": 80,
        "readTimeout": 7200,
        "writeTimeout": 7200,
        "cacheCategory": "redis",
        "runtimeDir": "runtime",
        "uploadDir": "runtime/upload",
        "downloadDir": "runtime/download",
        "kubeConfDir": "conf/kubeconf"
      },
      "jenkins": {
        "host": "https://jenkins2.firstshare.cn",
        "username": "FSSvcA053",
        "password": "w8KOC5Q007F&",
        "jobBuildImage": "k8s-app-image-build",
        "jobBuildPackageToImage": "k8s-app-package-build-to-image",
        "jobJacoco": "k8s-jacoco",
        "mavenImage": "reg.firstshare.cn/base/fs-maven3.9",
        "enableCRSF": true
      },
      "gitlab": {
        "host": "https://git.firstshare.cn",
        "token": "**************************"
      },
      "cms": {
        "webHost": "https://oss.firstshare.cn/cms",
        "apiHost": "https://oss.firstshare.cn/cs/api",
        "token": "k8s-manager-jnM2ShB5aE6BRBAtGSDc",
        "cmdbConfigPath": "firstshare/cmdb-mark-v2.json",
        "serviceConfigPath": "firstshare/k8s-service-list.csv",
        "serviceConfigPathV2": "firstshare/k8s-service-list-v2.csv"
      },
      "harbor": {
        "host": "reg.firstshare.cn",
        "appProject": "app",
        "helmChartProject": "chartrepo",
        "https": true,
        "username": "robot$k8s-app-manager",
        "password": "MrBNqpKtYAuBt8SvW8KLEmHeRTjSKx8S",
        "artifactBaseImage": "reg.firstshare.cn/base/fs-artifact-repo:v2.0",
        "artifactProject": "artifact"
      },
      "postgres": {
        "host": "*************",
        "port": 5432,
        "user": "fs_pgdb_a_u_k8sapp",
        "password": "d7QanjikTW09J2BOrebS",
        "database": "fs_k8s_app_manager"
      },
      "cas": {
        "loginPath": "http://oss.firstshare.cn/cas/login",
        "logoutPath": "http://oss.firstshare.cn/cas/logout",
        "validatePath": "http://oss.firstshare.cn/cas/p3/serviceValidate"
      },
      "qiXin": {
        "host": "",
        "publishAppId": "publish",
        "importantAlertAppId": "FSAID_131809b",
        "receiverEI": [
          5548
        ]
      },
      "sms": {
        "host": ""
      },
      "fsPaas": {
        "host": "",
        "objectId": "object_op_deployRec__c",
        "enterpriseId": 1,
        "sysUserId": -10000,
        "qiXinSessionFieldId": "5e845a05da7f1c0001ae82ac"
      },
      "metadataServer": {
        "host": "",
        "userObj": "",
        "deptObj": "",
        "serverModuleObj": ""
      },
      "k8sAppManager": {
        "host": "https://k8s-app.firstshare.cn"
      },
      "war": {
        "host": "https://war.firstshare.cn",
        "username": "jviCkGFDrOszjKri",
        "password": "ECvpVYQ56cm1MivKVJSPkA"
      },
      "eolinker": {
        "enable": false,
        "host": "https://eolinker.foneshare.cn",
        "eoSecretKey": "mcb1MkL1821ccf52f63fe1ec701f30c14329a54a2a85ab0",
        "spaceIdDefault": "Ns7aIIHd8ab5307649b503da006ccd3a1ec58709b7090a2",
        "reportHost": ""
      },
      "redis": {
        "addr": "{{DEPLOY_APP_NAME}}-redis:6379",
        "password": "",
        "db": 0
      },
      "kafka": {
        "addr": [
          "**************:9092",
          "**************:9092",
          "**************:9092"
        ]
      },
      "clickhouse": {
        "addr": [
          "*************:9000",
          "*************:9000",
          "*************:9000"
        ],
        "user": "",
        "password": ""
      },
      "openApiTokens": [
        "function__B2rvuWCLtQSOUEao"
      ]
    }
  oncall.json: |-
    {
      "enable": true,
      "scaleUp": {
        "minCPUAvailableCore": 20,
        "minMemoryAvailableGB": 50
      },
      "alertList": [
        {
          "name": "pod-restart-oom-webhook",
          "desc": "OOM次数-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [
              "*/*/fs-paas-process-orch-task",
              "*/*/fs-crm-sfa-rest",
              "*/*/fs-paas-app-udobj-02",
              "*/*/fs-mq-bus",
              "*/*/fs-organization-provider-4data-auth",
              "*/*/data-auth-service-bi",
              "*/*/paas-pg-scanner",
              "*/*/paas-leica-sync",
              "*/*/i18n-service",
              "*/*/fs-webpage-customer-provider",
              "*/*/fs-user-login-biz",
              "*/*/fs-uc-provider",
              "*/*/fs-stone4cloud",
              "*/*/fs-stone-proxy",
              "*/*/fs-stone-metaserver",
              "*/*/fs-stone-fileserver",
              "*/*/fs-stone-dataserver",
              "*/*/fs-stone-cloud",
              "*/*/fs-refresh-es-data",
              "*/*/fs-polling-cgi",
              "*/*/fs-pod-service",
              "*/*/fs-plat-user-login-provider",
              "*/*/fs-plat-user-login-cgi",
              "*/*/fs-plat-social-cloud",
              "*/*/fs-plat-polling-cloud",
              "*/*/fs-plat-org-management",
              "*/*/fs-plat-login-cloud",
              "*/*/fs-plat-fap",
              "*/*/fs-plat-app-view-cgi",
              "*/*/fs-paas-workflow-processor",
              "*/*/fs-paas-workflow-cloud",
              "*/*/fs-paas-workflow",
              "*/*/fs-paas-license-surrogate",
              "*/*/fs-paas-license",
              "*/*/fs-paas-bizconf-web",
              "*/*/fs-paas-auth-provider",
              "*/*/fs-paas-app-udobj-rest",
              "*/*/fs-paas-app-udobj",
              "*/*/fs-organization-provider",
              "*/*/fs-metadata-rest-cloud",
              "*/*/fs-metadata-rest",
              "*/*/fs-crm-sfa",
              "*/*/fs-cep-provider",
              "*/*/fs-bpm-after-action",
              "*/*/fs-apibus-paas",
              "*/*/fs-apibus-ncrm",
              "*/*/fs-apibus-global",
              "*/*/fs-active-session-manage",
              "*/*/fast-notifier",
              "*/*/data-auth-worker",
              "*/*/data-auth-service"
            ],
            "blacklist": []
          }
        },
        {
          "name": "jvm-gc-more",
          "desc": "GC时长-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "jvm-old-gc-more",
          "desc": "GC次数-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "log-error-webhook",
          "desc": "服务错误-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "log-apibus-error-service-webhook",
          "desc": "服务提供者apibus错误-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "log-cep-error-service-webhook",
          "desc": "服务cep错误-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "pod-liveness-failed-webhook",
          "desc": "存活探针失败-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "jvm-gc-more",
          "desc": "GC耗时-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "jvm-old-gc-more",
          "desc": "GC次数-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "k8s-node-mem-usage-webhook",
          "desc": "宿主机内存使用率-自愈-驱逐",
          "dryRun": false,
          "appList": {
            "whitelist": [
              "*/*/fs-file-datax-stats",
              "*/*/fs-crm-reconciliation",
              "*/*/bi-console-compose",
              "*/*/fs-eye-radar",
              "*/*/fs-sail-order-task",
              "*/*/short-url-sync-debug",
              "*/*/fmcg-data-strategy-server",
              "*/*/fs-flow-session-task",
              "*/*/fs-paas-action-centre",
              "*/*/fs-paas-resource",
              "*/*/fs-out-identity-provider",
              "*/*/fs-file-process",
              "*/*/fs-international-connector",
              "*/*/fs-idp-helper",
              "*/*/fs-crm-meeting",
              "*/*/fs-eye-manager",
              "*/*/fs-paas-ai-task",
              "*/*/egress-api-gateway",
              "*/*/egress-proxy-management",
              "*/*/fs-egress-api-notify-push",
              "*/*/fs-crm-fmcg-reward-web",
              "*/*/fs-crm-cloud",
              "*/*/fs-sail-order-cloud",
              "*/*/surey",
              "*/*/form-service",
              "*/*/fs-crm-fmcg-service-urgent",
              "*/*/fs-fmcg-sales-mengniu-urgent",
              "*/*/fs-checkins-biz-gray",
              "*/*/metadata-console-compose",
              "*/*/fs-crm-fmcg-service-gray",
              "*/*/fs-fmcg-sales-cgi-gray",
              "*/*/egress-proxy-service",
              "*/*/fs-fmcg-public-abutment",
              "*/*/fs-paas-wishful-cloud",
              "*/*/fs-devops-console",
              "*/*/socketlog-service",
              "*/*/fs-qixin-web-check",
              "*/*/fs-expression-provider",
              "*/*/fs-task-manage-provider",
              "*/*/sync-data-mq-provider",
              "*/*/fs-enterprise-relation-biz-global",
              "*/*/hamster-server",
              "*/*/fs-erp-sync-data-monitor",
              "*/*/fs-paas-function-template",
              "*/*/fs-document-convert-big",
              "*/*/fs-paas-recording-provider",
              "*/*/fs-paas-function-runtime-pod",
              "*/*/fs-document-convert",
              "*/*/fs-qixin-console",
              "*/*/fs-support",
              "*/*/fs-open-work-order-paas-web",
              "*/*/oauth-center",
              "*/*/datax-sync",
              "*/*/hospital-spider",
              "*/*/fs-plat-auth-task",
              "*/*/fs-plat-auth-biz",
              "*/*/checkins-v2-mq-dispatcher",
              "*/*/fs-open-huawei-imc-sync",
              "*/*/fs-plat-feeds-next-cloud",
              "*/*/dev-online-tool",
              "*/*/fs-new-schedule-async",
              "*/*/fs-new-schedule",
              "*/*/fs-crm-call-center",
              "*/*/fs-fmcg-customized-nestle",
              "*/*/fs-paas-wishful",
              "*/*/fs-recognizecard",
              "*/*/weex-config-console",
              "*/*/user-center",
              "*/*/sfa-console",
              "*/*/pg-scanner-ui",
              "*/*/paas-datax-pusher",
              "*/*/paas-console",
              "*/*/paas-bi-copier",
              "*/*/open-api-service-allprovider",
              "*/*/open-api-gateway-web-file",
              "*/*/open-api-admin-web",
              "*/*/msg-center",
              "*/*/log-level-manager",
              "*/*/ibss-site-task",
              "*/*/ibss-site-platform",
              "*/*/ibss-service-qms",
              "*/*/ibss-service-gm",
              "*/*/ibss-service-cs",
              "*/*/ibss-service-cas",
              "*/*/i18n-message",
              "*/*/i18n-cgi",
              "*/*/fs-xtcrm-gateway-provider",
              "*/*/fs-xt-proxy",
              "*/*/fs-xshell-compose",
              "*/*/fs-work-report-provider",
              "*/*/fs-wechat-union-all",
              "*/*/fs-wechat-sender-task",
              "*/*/fs-wechat-proxy-callback",
              "*/*/fs-warehouse-statistic",
              "*/*/fs-warehouse-provider",
              "*/*/fs-warehouse-processor",
              "*/*/fs-warehouse-enterprise-statistic",
              "*/*/fs-warehouse-batch",
              "*/*/fs-warehouse-avatar",
              "*/*/fs-uc-biz",
              "*/*/fs-sync-data-task",
              "*/*/fs-stone-upload-consumer",
              "*/*/fs-stone-transfer",
              "*/*/fs-stone-thumbnail-proxy",
              "*/*/fs-stone-backup-s3",
              "*/*/fs-stone-audioserver",
              "*/*/fs-stone-admin",
              "*/*/fs-ssh-discmd",
              "*/*/fs-social-task",
              "*/*/fs-service-console",
              "*/*/fs-salarybill",
              "*/*/fs-register-provider",
              "*/*/fs-register-cgi",
              "*/*/fs-qixin-task-provider",
              "*/*/fs-qixin-task-brush-db-provider",
              "*/*/fs-qixin-search-provider",
              "*/*/fs-qixin-search-message-provider",
              "*/*/fs-qixin-search-message-biz",
              "*/*/fs-qixin-push-broker",
              "*/*/fs-qixin-plugin",
              "*/*/fs-qixin-objgroup-manage-provider",
              "*/*/fs-qixin-objgroup-manage-biz",
              "*/*/fs-qixin-extension-provider",
              "*/*/fs-qixin-ext-compose",
              "*/*/fs-qixin-business-broker",
              "*/*/fs-qixin-bot-provider",
              "*/*/fs-qixin-bot-manage",
              "*/*/fs-qixin-bot-crm-helper",
              "*/*/fs-qixin-bot-cloud",
              "*/*/fs-pod-console",
              "*/*/fs-plat-service-cloud",
              "*/*/fs-plat-service-biz",
              "*/*/fs-plat-sandbox-provider",
              "*/*/fs-plat-netdisk-provider",
              "*/*/fs-plat-netdisk-biz",
              "*/*/fs-plat-management-biz",
              "*/*/fs-plat-feeds-cloud",
              "*/*/fs-plat-checknet-cgi",
              "*/*/fs-pidops",
              "*/*/fs-paas-score",
              "*/*/fs-paas-refresh-forest",
              "*/*/fs-paas-recycle",
              "*/*/fs-paas-org",
              "*/*/fs-paas-mongo-sharding-transfer",
              "*/*/fs-paas-function-service-debug",
              "*/*/fs-paas-batch-web",
              "*/*/fs-paas-app-task",
              "*/*/fs-organization-adapter-hj",
              "*/*/fs-open-webhook-message-send",
              "*/*/fs-open-webhook-accountsync",
              "*/*/fs-open-material-web",
              "*/*/fs-open-jobs-executor",
              "*/*/fs-open-intelligence-form-provider",
              "*/*/fs-open-huawei-gateway",
              "*/*/fs-open-assets-all",
              "*/*/fs-online-consult-externals",
              "*/*/fs-oauth-base-provider",
              "*/*/fs-notice",
              "*/*/fs-myfavourites-provider",
              "*/*/fs-myfavourites-biz",
              "*/*/fs-metadata-reference",
              "*/*/fs-metadata-option",
              "*/*/fs-metadata-for-test",
              "*/*/fs-metadata-for-refresh",
              "*/*/fs-message-wechat",
              "*/*/fs-message-server-wrapper",
              "*/*/fs-message-kingdee",
              "*/*/fs-message-external",
              "*/*/fs-meeting-assistant-task",
              "*/*/fs-meeting-assistant-bizserver",
              "*/*/fs-loki-router",
              "*/*/fs-leica-web",
              "*/*/fs-leica-index",
              "*/*/fs-landray-oa-web",
              "*/*/fs-kiscloud-web",
              "*/*/fs-kiscloud-task",
              "*/*/fs-kiscloud-provider",
              "*/*/fs-kiscloud-consumer",
              "*/*/fs-jsapi-fcpserver",
              "*/*/fs-job-admin",
              "*/*/fs-invite-biz",
              "*/*/fs-integral-task",
              "*/*/fs-integral-provider-urgent",
              "*/*/fs-integral-provider",
              "*/*/fs-image-server",
              "*/*/fs-id-server",
              "*/*/fs-fsc-provider",
              "*/*/fs-fmcg-efficiency-cgi-urgent",
              "*/*/fs-fmcg-efficiency-cgi",
              "*/*/fs-fmcg-customized-excel",
              "*/*/fs-fmcg-customized-biz",
              "*/*/fs-file-preview-service",
              "*/*/fs-feeds-notice",
              "*/*/fs-eye-consumer",
              "*/*/fs-ext-contact-provider",
              "*/*/fs-enterprise-admin-provider",
              "*/*/fs-document-preview",
              "*/*/fs-dingtalk-client",
              "*/*/fs-db-copier",
              "*/*/fs-customer-component-provider",
              "*/*/fs-cross-enterprise-sharedisk",
              "*/*/fs-crm-template",
              "*/*/fs-crm-smartform",
              "*/*/fs-crm-notify-provider",
              "*/*/fs-crm-metadata",
              "*/*/fs-crm-integral-task",
              "*/*/fs-crm-integral",
              "*/*/fs-crm-fmcg",
              "*/*/fs-console-compose",
              "*/*/fs-cloud-operation-provider",
              "*/*/fs-cloud-operation-biz",
              "*/*/fs-checkins-provider",
              "*/*/fs-checkins-biz",
              "*/*/fs-chconnector-provider",
              "*/*/fs-cas-overlay",
              "*/*/fs-bug-feedback",
              "*/*/fs-broker-web",
              "*/*/fs-big-file-manager-biz",
              "*/*/fs-bi-task-new",
              "*/*/fs-bi-task",
              "*/*/fs-bi-stat-transfer",
              "*/*/fs-bi-refresh",
              "*/*/fs-bi-provider",
              "*/*/fs-bi-permission",
              "*/*/fs-bi-mq",
              "*/*/fs-bi-devops",
              "*/*/fs-audit-log-biz",
              "*/*/fs-appserver-trigger-provider",
              "*/*/fs-appserver-trigger",
              "*/*/fs-appserver-schedule-provider",
              "*/*/fs-appserver-pm-scheduledtask",
              "*/*/fs-appserver-pm-bizserver",
              "*/*/fs-appserver-payment-web",
              "*/*/fs-appserver-payment-task",
              "*/*/fs-appserver-openproxy-web",
              "*/*/fs-appserver-holiday-v2-task",
              "*/*/fs-appserver-holiday-v2",
              "*/*/fs-appserver-comment-web",
              "*/*/fs-appserver-checkins-v2-urgent",
              "*/*/fs-appserver-checkins-v2-gray",
              "*/*/fs-appserver-battlereport-assistant-web",
              "*/*/fs-account-statement",
              "*/*/config-web",
              "*/*/checkins-v2-task",
              "*/*/checkins-v2-history",
              "*/*/checkins-office-v2-task",
              "*/*/checkins-office-stat-job",
              "*/*/auditlog-service",
              "*/*/auditlog-query-service",
              "*/*/async-job-center-cloud",
              "*/*/action-router-console",
              "*/*/fs-bi-transfer",
              "*/*/fs-k8s-tomcat-test"
            ],
            "blacklist": []
          }
        },
        {
          "name": "node-load1-k8s-webhook",
          "desc": "宿主机CPU负载-自愈-驱逐",
          "dryRun": false,
          "appList": {
            "whitelist": [
              "*/*/fs-file-datax-stats",
              "*/*/fs-crm-reconciliation",
              "*/*/bi-console-compose",
              "*/*/fs-eye-radar",
              "*/*/fs-sail-order-task",
              "*/*/short-url-sync-debug",
              "*/*/fmcg-data-strategy-server",
              "*/*/fs-flow-session-task",
              "*/*/fs-paas-action-centre",
              "*/*/fs-paas-resource",
              "*/*/fs-out-identity-provider",
              "*/*/fs-file-process",
              "*/*/fs-international-connector",
              "*/*/fs-idp-helper",
              "*/*/fs-crm-meeting",
              "*/*/fs-eye-manager",
              "*/*/fs-paas-ai-task",
              "*/*/egress-api-gateway",
              "*/*/egress-proxy-management",
              "*/*/fs-egress-api-notify-push",
              "*/*/fs-crm-fmcg-reward-web",
              "*/*/fs-crm-cloud",
              "*/*/fs-sail-order-cloud",
              "*/*/surey",
              "*/*/form-service",
              "*/*/fs-crm-fmcg-service-urgent",
              "*/*/fs-fmcg-sales-mengniu-urgent",
              "*/*/fs-checkins-biz-gray",
              "*/*/metadata-console-compose",
              "*/*/fs-crm-fmcg-service-gray",
              "*/*/fs-fmcg-sales-cgi-gray",
              "*/*/egress-proxy-service",
              "*/*/fs-fmcg-public-abutment",
              "*/*/fs-paas-wishful-cloud",
              "*/*/fs-devops-console",
              "*/*/socketlog-service",
              "*/*/fs-qixin-web-check",
              "*/*/fs-expression-provider",
              "*/*/fs-task-manage-provider",
              "*/*/sync-data-mq-provider",
              "*/*/fs-enterprise-relation-biz-global",
              "*/*/hamster-server",
              "*/*/fs-erp-sync-data-monitor",
              "*/*/fs-paas-function-template",
              "*/*/fs-document-convert-big",
              "*/*/fs-paas-recording-provider",
              "*/*/fs-paas-function-runtime-pod",
              "*/*/fs-document-convert",
              "*/*/fs-qixin-console",
              "*/*/fs-support",
              "*/*/fs-open-work-order-paas-web",
              "*/*/oauth-center",
              "*/*/datax-sync",
              "*/*/hospital-spider",
              "*/*/fs-plat-auth-task",
              "*/*/fs-plat-auth-biz",
              "*/*/checkins-v2-mq-dispatcher",
              "*/*/fs-open-huawei-imc-sync",
              "*/*/fs-plat-feeds-next-cloud",
              "*/*/dev-online-tool",
              "*/*/fs-new-schedule-async",
              "*/*/fs-new-schedule",
              "*/*/fs-crm-call-center",
              "*/*/fs-fmcg-customized-nestle",
              "*/*/fs-paas-wishful",
              "*/*/fs-recognizecard",
              "*/*/weex-config-console",
              "*/*/user-center",
              "*/*/sfa-console",
              "*/*/pg-scanner-ui",
              "*/*/paas-datax-pusher",
              "*/*/paas-console",
              "*/*/paas-bi-copier",
              "*/*/open-api-service-allprovider",
              "*/*/open-api-gateway-web-file",
              "*/*/open-api-admin-web",
              "*/*/msg-center",
              "*/*/log-level-manager",
              "*/*/ibss-site-task",
              "*/*/ibss-site-platform",
              "*/*/ibss-service-qms",
              "*/*/ibss-service-gm",
              "*/*/ibss-service-cs",
              "*/*/ibss-service-cas",
              "*/*/i18n-message",
              "*/*/i18n-cgi",
              "*/*/fs-xtcrm-gateway-provider",
              "*/*/fs-xt-proxy",
              "*/*/fs-xshell-compose",
              "*/*/fs-work-report-provider",
              "*/*/fs-wechat-union-all",
              "*/*/fs-wechat-sender-task",
              "*/*/fs-wechat-proxy-callback",
              "*/*/fs-warehouse-statistic",
              "*/*/fs-warehouse-provider",
              "*/*/fs-warehouse-processor",
              "*/*/fs-warehouse-enterprise-statistic",
              "*/*/fs-warehouse-batch",
              "*/*/fs-warehouse-avatar",
              "*/*/fs-uc-biz",
              "*/*/fs-sync-data-task",
              "*/*/fs-stone-upload-consumer",
              "*/*/fs-stone-transfer",
              "*/*/fs-stone-thumbnail-proxy",
              "*/*/fs-stone-backup-s3",
              "*/*/fs-stone-audioserver",
              "*/*/fs-stone-admin",
              "*/*/fs-ssh-discmd",
              "*/*/fs-social-task",
              "*/*/fs-service-console",
              "*/*/fs-salarybill",
              "*/*/fs-register-provider",
              "*/*/fs-register-cgi",
              "*/*/fs-qixin-task-provider",
              "*/*/fs-qixin-task-brush-db-provider",
              "*/*/fs-qixin-search-provider",
              "*/*/fs-qixin-search-message-provider",
              "*/*/fs-qixin-search-message-biz",
              "*/*/fs-qixin-push-broker",
              "*/*/fs-qixin-plugin",
              "*/*/fs-qixin-objgroup-manage-provider",
              "*/*/fs-qixin-objgroup-manage-biz",
              "*/*/fs-qixin-extension-provider",
              "*/*/fs-qixin-ext-compose",
              "*/*/fs-qixin-business-broker",
              "*/*/fs-qixin-bot-provider",
              "*/*/fs-qixin-bot-manage",
              "*/*/fs-qixin-bot-crm-helper",
              "*/*/fs-qixin-bot-cloud",
              "*/*/fs-pod-console",
              "*/*/fs-plat-service-cloud",
              "*/*/fs-plat-service-biz",
              "*/*/fs-plat-sandbox-provider",
              "*/*/fs-plat-netdisk-provider",
              "*/*/fs-plat-netdisk-biz",
              "*/*/fs-plat-management-biz",
              "*/*/fs-plat-feeds-cloud",
              "*/*/fs-plat-checknet-cgi",
              "*/*/fs-pidops",
              "*/*/fs-paas-score",
              "*/*/fs-paas-refresh-forest",
              "*/*/fs-paas-recycle",
              "*/*/fs-paas-org",
              "*/*/fs-paas-mongo-sharding-transfer",
              "*/*/fs-paas-function-service-debug",
              "*/*/fs-paas-batch-web",
              "*/*/fs-paas-app-task",
              "*/*/fs-organization-adapter-hj",
              "*/*/fs-open-webhook-message-send",
              "*/*/fs-open-webhook-accountsync",
              "*/*/fs-open-material-web",
              "*/*/fs-open-jobs-executor",
              "*/*/fs-open-intelligence-form-provider",
              "*/*/fs-open-huawei-gateway",
              "*/*/fs-open-assets-all",
              "*/*/fs-online-consult-externals",
              "*/*/fs-oauth-base-provider",
              "*/*/fs-notice",
              "*/*/fs-myfavourites-provider",
              "*/*/fs-myfavourites-biz",
              "*/*/fs-metadata-reference",
              "*/*/fs-metadata-option",
              "*/*/fs-metadata-for-test",
              "*/*/fs-metadata-for-refresh",
              "*/*/fs-message-wechat",
              "*/*/fs-message-server-wrapper",
              "*/*/fs-message-kingdee",
              "*/*/fs-message-external",
              "*/*/fs-meeting-assistant-task",
              "*/*/fs-meeting-assistant-bizserver",
              "*/*/fs-loki-router",
              "*/*/fs-leica-web",
              "*/*/fs-leica-index",
              "*/*/fs-landray-oa-web",
              "*/*/fs-kiscloud-web",
              "*/*/fs-kiscloud-task",
              "*/*/fs-kiscloud-provider",
              "*/*/fs-kiscloud-consumer",
              "*/*/fs-jsapi-fcpserver",
              "*/*/fs-job-admin",
              "*/*/fs-invite-biz",
              "*/*/fs-integral-task",
              "*/*/fs-integral-provider-urgent",
              "*/*/fs-integral-provider",
              "*/*/fs-image-server",
              "*/*/fs-id-server",
              "*/*/fs-fsc-provider",
              "*/*/fs-fmcg-efficiency-cgi-urgent",
              "*/*/fs-fmcg-efficiency-cgi",
              "*/*/fs-fmcg-customized-excel",
              "*/*/fs-fmcg-customized-biz",
              "*/*/fs-file-preview-service",
              "*/*/fs-feeds-notice",
              "*/*/fs-eye-consumer",
              "*/*/fs-ext-contact-provider",
              "*/*/fs-enterprise-admin-provider",
              "*/*/fs-document-preview",
              "*/*/fs-dingtalk-client",
              "*/*/fs-db-copier",
              "*/*/fs-customer-component-provider",
              "*/*/fs-cross-enterprise-sharedisk",
              "*/*/fs-crm-template",
              "*/*/fs-crm-smartform",
              "*/*/fs-crm-notify-provider",
              "*/*/fs-crm-metadata",
              "*/*/fs-crm-integral-task",
              "*/*/fs-crm-integral",
              "*/*/fs-crm-fmcg",
              "*/*/fs-console-compose",
              "*/*/fs-cloud-operation-provider",
              "*/*/fs-cloud-operation-biz",
              "*/*/fs-checkins-provider",
              "*/*/fs-checkins-biz",
              "*/*/fs-chconnector-provider",
              "*/*/fs-cas-overlay",
              "*/*/fs-bug-feedback",
              "*/*/fs-broker-web",
              "*/*/fs-big-file-manager-biz",
              "*/*/fs-bi-task-new",
              "*/*/fs-bi-task",
              "*/*/fs-bi-stat-transfer",
              "*/*/fs-bi-refresh",
              "*/*/fs-bi-provider",
              "*/*/fs-bi-permission",
              "*/*/fs-bi-mq",
              "*/*/fs-bi-devops",
              "*/*/fs-audit-log-biz",
              "*/*/fs-appserver-trigger-provider",
              "*/*/fs-appserver-trigger",
              "*/*/fs-appserver-schedule-provider",
              "*/*/fs-appserver-pm-scheduledtask",
              "*/*/fs-appserver-pm-bizserver",
              "*/*/fs-appserver-payment-web",
              "*/*/fs-appserver-payment-task",
              "*/*/fs-appserver-openproxy-web",
              "*/*/fs-appserver-holiday-v2-task",
              "*/*/fs-appserver-holiday-v2",
              "*/*/fs-appserver-comment-web",
              "*/*/fs-appserver-checkins-v2-urgent",
              "*/*/fs-appserver-checkins-v2-gray",
              "*/*/fs-appserver-battlereport-assistant-web",
              "*/*/fs-account-statement",
              "*/*/config-web",
              "*/*/checkins-v2-task",
              "*/*/checkins-v2-history",
              "*/*/checkins-office-v2-task",
              "*/*/checkins-office-stat-job",
              "*/*/auditlog-service",
              "*/*/auditlog-query-service",
              "*/*/async-job-center-cloud",
              "*/*/action-router-console",
              "*/*/fs-bi-transfer",
              "*/*/fs-k8s-tomcat-test"
            ],
            "blacklist": []
          }
        },
        {
          "name": "app-available-replicas3-webhook",
          "desc": "服务可用率-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [
              "*/*/fs-paas-process-orch-task",
              "*/*/fs-crm-sfa-rest",
              "*/*/fs-paas-app-udobj-02",
              "*/*/fs-mq-bus",
              "*/*/fs-organization-provider-4data-auth",
              "*/*/data-auth-service-bi",
              "*/*/paas-pg-scanner",
              "*/*/paas-leica-sync",
              "*/*/i18n-service",
              "*/*/fs-webpage-customer-provider",
              "*/*/fs-user-login-biz",
              "*/*/fs-uc-provider",
              "*/*/fs-stone4cloud",
              "*/*/fs-stone-proxy",
              "*/*/fs-stone-metaserver",
              "*/*/fs-stone-fileserver",
              "*/*/fs-stone-dataserver",
              "*/*/fs-stone-cloud",
              "*/*/fs-refresh-es-data",
              "*/*/fs-polling-cgi",
              "*/*/fs-pod-service",
              "*/*/fs-plat-user-login-provider",
              "*/*/fs-plat-user-login-cgi",
              "*/*/fs-plat-social-cloud",
              "*/*/fs-plat-polling-cloud",
              "*/*/fs-plat-org-management",
              "*/*/fs-plat-login-cloud",
              "*/*/fs-plat-fap",
              "*/*/fs-plat-app-view-cgi",
              "*/*/fs-paas-workflow-processor",
              "*/*/fs-paas-workflow-cloud",
              "*/*/fs-paas-workflow",
              "*/*/fs-paas-license-surrogate",
              "*/*/fs-paas-license",
              "*/*/fs-paas-bizconf-web",
              "*/*/fs-paas-auth-provider",
              "*/*/fs-paas-app-udobj-rest",
              "*/*/fs-paas-app-udobj",
              "*/*/fs-organization-provider",
              "*/*/fs-metadata-rest-cloud",
              "*/*/fs-metadata-rest",
              "*/*/fs-crm-sfa",
              "*/*/fs-cep-provider",
              "*/*/fs-bpm-after-action",
              "*/*/fs-apibus-paas",
              "*/*/fs-apibus-ncrm",
              "*/*/fs-apibus-global",
              "*/*/fs-active-session-manage",
              "*/*/fast-notifier",
              "*/*/data-auth-worker",
              "*/*/data-auth-service",
              "*/*/fs-plat-org-management-function",
              "*/*/fs-crm-manufacturing-rest",
              "*/*/fs-flow-orch",
              "*/*/fs-fmcg-ai-server",
              "*/*/fast-notifier-qixin",
              "*/*/fs-flow-vote",
              "*/*/outer-oa-connector-web",
              "*/*/fs-bi-agent",
              "*/*/fs-crm-task-sfa-ai",
              "*/*/fs-huawei-kit-web",
              "*/*/fs-open-qywx-all",
              "*/*/fs-open-custom-hisense-decrypt-file",
              "*/*/fs-file-server",
              "*/*/fs-crm-all",
              "*/*/fs-stone-auth",
              "*/*/fs-bi-warehouse",
              "*/*/fs-ui-paas-cloud",
              "*/*/erp-connector-proxy",
              "*/*/fs-crm-fmcg-sales",
              "*/*/egress-api-service",
              "*/*/fs-paas-ai-provider",
              "*/*/fs-enterprise-relation-biz-login",
              "*/*/fs-manufacturing-task",
              "*/*/fs-erp-sync-data-file",
              "*/*/fs-mq-sort",
              "*/*/fs-crm-fmcg-wq",
              "*/*/fs-erp-order-contacts-proxy",
              "*/*/fs-feishu-provider",
              "*/*/fs-feishu-web",
              "*/*/fs-paas-function-service-background-provider",
              "*/*/fs-crm-task-lto-sfa",
              "*/*/fs-erp-oa",
              "*/*/fs-paas-app-udobj-rest4realtime",
              "*/*/fs-eservice-mq-listener",
              "*/*/fs-organziation-adapter-4orgbiz",
              "*/*/config-service-handle",
              "*/*/fs-qixin-file",
              "*/*/fs-erp-sync-data-web",
              "*/*/fs-open-dingtalk",
              "*/*/fs-sail-order",
              "*/*/fs-dingtalk-cloud",
              "*/*/fs-dingtalk-all",
              "*/*/fs-open-qywx-save-message",
              "*/*/fs-crm-import-manufacturing",
              "*/*/fs-crm-manufacturing",
              "*/*/fs-open-custom",
              "*/*/fs-erp-sync-data-custom",
              "*/*/fs-flow-processor",
              "*/*/fs-metadata-provider",
              "*/*/fs-crm-fmcg-service",
              "*/*/weex-console-service",
              "*/*/tenant-sandbox",
              "*/*/qywx-message-send-provider",
              "*/*/qywx-event-handler-web",
              "*/*/qywx-account-sync-provider",
              "*/*/qywx-account-bind-provider",
              "*/*/paas-db-operator",
              "*/*/open-api-gateway-web",
              "*/*/i18n-setting",
              "*/*/fs-workflow-processor",
              "*/*/fs-workflow",
              "*/*/fs-warehouse-cross",
              "*/*/fs-user-extension-provider",
              "*/*/fs-user-extension-biz",
              "*/*/fs-todo",
              "*/*/fs-sync-data-all",
              "*/*/fs-stone-cgi",
              "*/*/fs-stage-propeller-processor",
              "*/*/fs-stage-propeller-cloud",
              "*/*/fs-stage-propeller",
              "*/*/fs-social-feeds",
              "*/*/fs-scheduler-task-provider",
              "*/*/fs-sail-server",
              "*/*/fs-qixin-web",
              "*/*/fs-qixin-provider",
              "*/*/fs-qixin-cloud",
              "*/*/fs-qixin-biz-web",
              "*/*/fs-plat-webhook-provider",
              "*/*/fs-plat-service-provider",
              "*/*/fs-plat-organization-cloud",
              "*/*/fs-plat-org-adapter-provider",
              "*/*/fs-paas-web",
              "*/*/fs-paas-sysmanage-compose",
              "*/*/fs-paas-rule",
              "*/*/fs-paas-metadata-dataloader",
              "*/*/fs-paas-job-schedule",
              "*/*/fs-paas-gnomon-executor",
              "*/*/fs-paas-function-service-runtime-provider",
              "*/*/fs-paas-function-service-runtime",
              "*/*/fs-paas-calculate-task",
              "*/*/fs-paas-app-udobj-rest4flow",
              "*/*/fs-organization-biz",
              "*/*/fs-organization-adapter",
              "*/*/fs-open-msg-all",
              "*/*/fs-open-app-center-all",
              "*/*/fs-online-consult-web",
              "*/*/fs-online-consult-base",
              "*/*/fs-metadata-data",
              "*/*/fs-message-server-web",
              "*/*/fs-message-server-gray",
              "*/*/fs-message-server",
              "*/*/fs-marketing-web-kis",
              "*/*/fs-marketing-task",
              "*/*/fs-marketing-statistic-provider",
              "*/*/fs-marketing-statistic",
              "*/*/fs-marketing-qywx",
              "*/*/fs-marketing-provider",
              "*/*/fs-marketing",
              "*/*/fs-k3cloud-web",
              "*/*/fs-hubble-query",
              "*/*/fs-hubble-index",
              "*/*/fs-hubble-cloud",
              "*/*/fs-fsc4stone",
              "*/*/fs-fsc-cgi",
              "*/*/fs-fmcg-service",
              "*/*/fs-fmcg-sales-cgi",
              "*/*/fs-flow-inspection",
              "*/*/fs-flow",
              "*/*/fs-feeds-provider",
              "*/*/fs-feeds-biz",
              "*/*/fs-erp-sync-data-task",
              "*/*/fs-erp-sync-data",
              "*/*/fs-enterprise-relation-biz",
              "*/*/fs-dingtalk-web",
              "*/*/fs-dingtalk-provider",
              "*/*/fs-crm-workflow-processor",
              "*/*/fs-crm-workflow-cloud",
              "*/*/fs-crm-workflow",
              "*/*/fs-crm-task-web",
              "*/*/fs-crm-task-sfa",
              "*/*/fs-crm-recycling-task",
              "*/*/fs-crm-import-sfa",
              "*/*/fs-crm-import",
              "*/*/fs-crm",
              "*/*/fs-bpm-processor",
              "*/*/fs-bpm-cloud",
              "*/*/fs-bpm",
              "*/*/fs-bi-uitype",
              "*/*/fs-bi-udf-report",
              "*/*/fs-bi-stat",
              "*/*/fs-bi-org",
              "*/*/fs-bi-metadata",
              "*/*/fs-bi-export",
              "*/*/fs-bi-compose",
              "*/*/fs-bi-cloud",
              "*/*/fs-appserver-checkins-v2",
              "*/*/fs-ai-detector-provider",
              "*/*/config-service",
              "*/*/cloud-mq-proxy",
              "*/*/checkins-office-v2-server",
              "*/*/cctrl-center",
              "*/*/async-job-import",
              "*/*/async-job-export",
              "*/*/action-router-service"
            ],
            "blacklist": []
          }
        },
        {
          "name": "jvm-tomcat-http-blocked",
          "desc": "Tomcat的http请求排队-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [
              "*/*/fs-paas-process-orch-task",
              "*/*/fs-crm-sfa-rest",
              "*/*/fs-paas-app-udobj-02",
              "*/*/fs-mq-bus",
              "*/*/fs-organization-provider-4data-auth",
              "*/*/data-auth-service-bi",
              "*/*/paas-pg-scanner",
              "*/*/paas-leica-sync",
              "*/*/i18n-service",
              "*/*/fs-webpage-customer-provider",
              "*/*/fs-user-login-biz",
              "*/*/fs-uc-provider",
              "*/*/fs-stone4cloud",
              "*/*/fs-stone-proxy",
              "*/*/fs-stone-metaserver",
              "*/*/fs-stone-fileserver",
              "*/*/fs-stone-dataserver",
              "*/*/fs-stone-cloud",
              "*/*/fs-refresh-es-data",
              "*/*/fs-polling-cgi",
              "*/*/fs-pod-service",
              "*/*/fs-plat-user-login-provider",
              "*/*/fs-plat-user-login-cgi",
              "*/*/fs-plat-social-cloud",
              "*/*/fs-plat-polling-cloud",
              "*/*/fs-plat-org-management",
              "*/*/fs-plat-login-cloud",
              "*/*/fs-plat-fap",
              "*/*/fs-plat-app-view-cgi",
              "*/*/fs-paas-workflow-processor",
              "*/*/fs-paas-workflow-cloud",
              "*/*/fs-paas-workflow",
              "*/*/fs-paas-license-surrogate",
              "*/*/fs-paas-license",
              "*/*/fs-paas-bizconf-web",
              "*/*/fs-paas-auth-provider",
              "*/*/fs-paas-app-udobj-rest",
              "*/*/fs-paas-app-udobj",
              "*/*/fs-organization-provider",
              "*/*/fs-metadata-rest-cloud",
              "*/*/fs-metadata-rest",
              "*/*/fs-crm-sfa",
              "*/*/fs-cep-provider",
              "*/*/fs-bpm-after-action",
              "*/*/fs-apibus-paas",
              "*/*/fs-apibus-ncrm",
              "*/*/fs-apibus-global",
              "*/*/fs-active-session-manage",
              "*/*/fast-notifier",
              "*/*/data-auth-worker",
              "*/*/data-auth-service"
            ],
            "blacklist": []
          }
        },
        {
          "name": "k8s-event-pod-unhealthy",
          "desc": "健康检测失败-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [],
            "blacklist": []
          }
        },
        {
          "name": "pod-cpu-throttled",
          "desc": "CPU受限-自愈-自动扩容",
          "dryRun": false,
          "appList": {
            "whitelist": [
              "*/*/fs-paas-process-orch-task",
              "*/*/fs-crm-sfa-rest",
              "*/*/fs-paas-app-udobj-02",
              "*/*/fs-mq-bus",
              "*/*/fs-organization-provider-4data-auth",
              "*/*/data-auth-service-bi",
              "*/*/paas-pg-scanner",
              "*/*/paas-leica-sync",
              "*/*/i18n-service",
              "*/*/fs-webpage-customer-provider",
              "*/*/fs-user-login-biz",
              "*/*/fs-uc-provider",
              "*/*/fs-stone4cloud",
              "*/*/fs-stone-proxy",
              "*/*/fs-stone-metaserver",
              "*/*/fs-stone-fileserver",
              "*/*/fs-stone-dataserver",
              "*/*/fs-stone-cloud",
              "*/*/fs-refresh-es-data",
              "*/*/fs-polling-cgi",
              "*/*/fs-pod-service",
              "*/*/fs-plat-user-login-provider",
              "*/*/fs-plat-user-login-cgi",
              "*/*/fs-plat-social-cloud",
              "*/*/fs-plat-polling-cloud",
              "*/*/fs-plat-org-management",
              "*/*/fs-plat-login-cloud",
              "*/*/fs-plat-fap",
              "*/*/fs-plat-app-view-cgi",
              "*/*/fs-paas-workflow-processor",
              "*/*/fs-paas-workflow-cloud",
              "*/*/fs-paas-workflow",
              "*/*/fs-paas-license-surrogate",
              "*/*/fs-paas-license",
              "*/*/fs-paas-bizconf-web",
              "*/*/fs-paas-auth-provider",
              "*/*/fs-paas-app-udobj-rest",
              "*/*/fs-paas-app-udobj",
              "*/*/fs-organization-provider",
              "*/*/fs-metadata-rest-cloud",
              "*/*/fs-metadata-rest",
              "*/*/fs-crm-sfa",
              "*/*/fs-cep-provider",
              "*/*/fs-bpm-after-action",
              "*/*/fs-apibus-paas",
              "*/*/fs-apibus-ncrm",
              "*/*/fs-apibus-global",
              "*/*/fs-active-session-manage",
              "*/*/fast-notifier",
              "*/*/data-auth-worker",
              "*/*/data-auth-service"
            ],
            "blacklist": []
          }
        },
        {
          "name": "jvm-thread-blocked",
          "desc": "Pod-JVM线程阻塞",
          "dryRun": false,
          "appList": {
            "whitelist": [
              "*/*/*"
            ],
            "blacklist": []
          }
        }
      ]
    }
  lb-pools.json: |-
    [
      {
        "cluster": "k8s1",
        "defaultLBAddr": "10.112.5.251",
        "lbAllocEnable": false,
        "LBAllocList": [
          {
            "addr": "10.112.5.1",
            "appList": [
              "*/fs-k8s-tomcat-test"
            ],
            "name": "wuzh-test",
            "remark": "吴志辉测试"
          }
        ]
      }
    ]
  settings.json: |-
    {
      "maintain": {
        "ci": {
          "open": false,
          "desc": "系统升级，预计16:40恢复"
        },
        "cd": {
          "open": false,
          "desc": "系统升级，预计16:40恢复"
        }
      },
      "timeWindow": {
        "open": true,
        "excludeNamespaces": [
          "jacoco"
        ]
      },
      "envConfirmText": "",
      "maxSurgeForceFull": true,
      "eolinkerTestDefault": true,
      "ingressReloadAllowInDay": true,
      "helmChart": {
        "versionPrefix": "9.40."
      },
      "mavenImages": [
        "reg.firstshare.cn/base/fs-maven3.9:openjdk8",
        "reg.firstshare.cn/base/fs-maven3.9:openjdk11",
        "reg.firstshare.cn/base/fs-maven3.9:openjdk17",
        "reg.firstshare.cn/base/fs-maven3.9:openjdk21",
        "reg.firstshare.cn/base/fs-maven3.9:openjdk24",
        "reg.firstshare.cn/base/fs-maven3.9:dragonwell8",
        "reg.firstshare.cn/base/fs-maven3.9:dragonwell11",
        "reg.firstshare.cn/base/fs-maven3.9:dragonwell17"
      ],
      "clusters": [    
        {
          "name": "k8s1",
          "description": "纷享云",
          "version": "1.29",
          "podIPPrefix": "10.34.",
          "ingressTmpl": "ingress2.yaml.tmpl",
          "ingressParentHost": "",
          "enable": true,
          "cronScale": true,
          "showLBAddr": true,
          "scaleMaxReplicas": 10,
          "cloudCategory": "fxiaokeCloud",
          "opsDeploy": false,
          "schedulerName": "",
          "needImagePreheat": false,
          "imageRegistryProxy": "",
          "imagePullPolicy": "Always",
          "labels": [],
          "namespaces": [
            "fstest",
            "fstest01",
            "fstest02",
            "fstest03",
            "fstest04",
            "fstest-gray",
            "fstest-stage",
            "fstest-urgent",
            "fstest-vip",
            "fstest-metadata",
            "firstshare",
            "firstshare-gray",
            "sandbox",
            "jacoco"
          ],
          "baseImages": [
            "reg.firstshare.cn/base/fs-tomcat8:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat8:openjdk24",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat9:openjdk24",
            "reg.firstshare.cn/base/fs-tomcat10:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat10:openjdk24",
            "reg.firstshare.cn/base/fs-tomcat8-function:dragonwell8",
            "reg.firstshare.cn/base/fs-tomcat9-doc-converter:openjdk17",
            "reg.firstshare.cn/base/fs-tomcat9-doc-converter:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat10-doc-converter:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat10-doc-converter:openjdk24",
            "reg.firstshare.cn/base/fs-tomcat8-pdf2html:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-ffmpeg3.3.4:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-wkhtmltopdf:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8-beefly1.0.7:openjdk21",
            "reg.firstshare.cn/base/fs-tomcat8-function-beefly1.0.7:dragonwell8",
            "reg.firstshare.cn/base/fs-tomcat9-function:dragonwell8",
            "reg.firstshare.cn/base/fs-tomcat9-python3:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat9-python3:openjdk21",
            "reg.firstshare.cn/base/fs-web-shell:openjdk8",
            "reg.firstshare.cn/base/fs-tomcat8:ali-dragonwell8"
          ],
          "nodes": [
            {
              "name": "通用",
              "value": "",
              "remark": ""
            },
            {
              "name": "GPU",
              "value": "GPU",
              "remark": ""
            },
            {
              "name": "坏孩子",
              "value": "BadBoy",
              "remark": ""
            },
            {
              "name": "外网访问权限专用",
              "value": "EgressNode",
              "remark": ""
            }
          ],
          "emptyDir": {
            "tomcatLog": true,
            "appLogs": true
          },
          "mallocArena": {
            "enable": true,
            "max": 4
          },
          "apm": {
            "enable": false,
            "skyWalkingUI": "https://skywalking.firstshare.cn"
          },
          "prometheusMonitor": {
            "enable": true,
            "prometheus": "eye2",
            "metricsInterval": "30s",
            "scrapeTimeout": "5s",
            "metricsPath": "/actuator/prometheus"
          },
          "thirdServices": {
            "oomReportUrl": "https://k8s-app.firstshare.cn/api/pod/event/report",
            "dubboHelperUrl": "http://fs-dubbo-helper.firstshare/fs-dubbo-helper",
            "nacosHelperUrl": "http://fs-envoy-server.firstshare",
            "nacosServerUrl": "nacos://apiuser:1E3E40FABB53EDF1E9738EA798FAA3EAB7B19953AE3654AD@*************:8848,*************:8848,*************:8848",
            "prometheusHost": "https://prometheus.firstshare.cn",
            "grafana": {
              "host": "https://grafana.firstshare.cn",
              "prometheusDS": "prometheus",
              "clickHouseDS": "firstshare-clickHouse",
              "pyroscopeDS": "pyroscope"
            },
            "clickVisual": {
              "host": "https://log.firstshare.cn"
            }
          }
        }
      ],
      "thirdServices": {
        "webShellHost": "https://oss.firstshare.cn/fs-k8s-web-shell",
        "devopsEventUrl": "https://grafana.firstshare.cn/d/a10c8008-2deb-4ea5-863b-7c9d5c1c1f14/e4ba8b-e4bbb6-e4b8ad-e5bf83"
      },
      "appSuffix": {
        "jacoco": "-jacoco",
        "sandbox": "-sandbox",
        "fstest-gray": "-gray",
        "fstest-stage": "-stage",
        "fstest-urgent": "-urgent",
        "fstest-vip": "-vip"
      },
      "deployStrategies": [
        {
          "name": "滚动",
          "value": "ROLL_UPDATE"
        },
        {
          "name": "重建",
          "value": "RECREATE"
        }
      ],
      "oncallScaleUp": {
        "appList": [
          "*/*/fs-k8s-tomcat-test",
          "*/*/checkins-office-v2-server",
          "*/*/data-auth-service",
          "*/*/fast-notifier",
          "*/*/fs-apibus-global",
          "*/*/fs-apibus-ncrm",
          "*/*/fs-apibus-paas",
          "*/*/fs-appserver-checkins-v2",
          "*/*/fs-bi-crm-report-web",
          "*/*/fs-bi-sqlengine",
          "*/*/fs-bi-stat",
          "*/*/fs-bi-udf-report",
          "*/*/fs-bi-uitype",
          "*/*/fs-bpm",
          "*/*/fs-bpm-after-action",
          "*/*/fs-checkins-biz",
          "*/*/fs-crm",
          "*/*/fs-crm-fmcg-service",
          "*/*/fs-crm-fmcg-wq",
          "*/*/fs-crm-import",
          "*/*/fs-crm-import-sfa",
          "*/*/fs-crm-manufacturing",
          "*/*/fs-crm-sfa",
          "*/*/fs-crm-workflow",
          "*/*/fs-erp-sync-data",
          "*/*/fs-erp-sync-data-web",
          "*/*/fs-feeds-biz",
          "*/*/fs-feeds-provider",
          "*/*/fs-flow",
          "*/*/fs-fmcg-customized-excel",
          "*/*/fs-fmcg-sales-cgi",
          "*/*/fs-fmcg-service",
          "*/*/fs-metadata-option",
          "*/*/fs-metadata-rest",
          "*/*/fs-open-app-center-provider",
          "*/*/fs-organization-adapter",
          "*/*/fs-organization-biz",
          "*/*/fs-organization-provider",
          "*/*/fs-organization-provider-4data-auth",
          "*/*/fs-paas-app-udobj",
          "*/*/fs-paas-app-udobj-rest",
          "*/*/fs-paas-app-udobj-rest4flow",
          "*/*/fs-paas-app-udobj-rest4realtime",
          "*/*/fs-paas-auth-provider",
          "*/*/fs-paas-bizconf-web",
          "*/*/fs-paas-function-service-debug",
          "*/*/fs-paas-function-service-runtime",
          "*/*/fs-paas-function-service-runtime-provider",
          "*/*/fs-paas-org",
          "*/*/fs-paas-rule",
          "*/*/fs-paas-workflow",
          "*/*/fs-paas-workflow-processor",
          "*/*/fs-plat-auth-biz",
          "*/*/fs-plat-org-management",
          "*/*/fs-plat-service-biz",
          "*/*/fs-plat-service-provider",
          "*/*/fs-scheduler-task-provider",
          "*/*/fs-social-feeds",
          "*/*/fs-stage-propeller",
          "*/*/fs-stone-dataserver",
          "*/*/fs-stone-fileserver",
          "*/*/fs-stone-metaserver",
          "*/*/fs-stone-proxy",
          "*/*/fs-sync-data-all",
          "*/*/fs-user-extension-biz",
          "*/*/fs-webpage-customer-provider"
        ]
      },
      "baseImageGray":  {
        "imageTagSuffix": "-v250618",
        "appList": [
           "*/*/*"
        ]
      },
      "pipelineDefault": {
        "status": "audit",
        "cluster": "k8s1",
        "namespace": "fstest",
        "baseImage": "reg.firstshare.cn/base/fs-tomcat8:openjdk8",
        "replicas": 1,
        "deployStrategy": "ROLL_UPDATE",
        "resources": {
          "requestCPU": 0.2,
          "requestMemory": 256,
          "limitCPU": 0.4,
          "limitMemory": 512
        },
        "livenessProbe": {
          "enable": true,
          "initialDelaySeconds": 1800,
          "periodSeconds": 30,
          "timeoutSeconds": 5,
          "failureThreshold": 4,
          "successThreshold": 1
        },
        "readinessProbe": {
          "enable": true,
          "initialDelaySeconds": 10,
          "periodSeconds": 10,
          "timeoutSeconds": 5,
          "failureThreshold": 3,
          "successThreshold": 1
        },
        "startupProbe": {
          "enable": true,
          "initialDelaySeconds": 10,
          "periodSeconds": 10,
          "timeoutSeconds": 5,
          "failureThreshold": 180,
          "successThreshold": 1
        },
        "schedule": {
          "strategy": "PREFERRED",
          "node": ""
        },
        "pvc": {
          "enable": false,
          "name": "",
          "mountPath": ""
        },
        "envs": [
          {
            "name": "K8S_APP_NAME",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          },
          {
            "name": "K8S_PROCESS_NAME",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          },
          {
            "name": "ENVIRONMENT_TYPE",
            "value": "firstshare",
            "type": "SYSTEM"
          },
          {
            "name": "MACHINE_TYPE",
            "value": "DOCKER",
            "type": "SYSTEM"
          },
          {
            "name": "CMS_ENV_TYPE",
            "value": "firstshare",
            "type": "SYSTEM"
          },
          {
            "name": "CATALINA_OPTS",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          },
          {
            "name": "JAVA_OPTS",
            "value": "[系统赋值]",
            "type": "SYSTEM"
          }
        ],
        "ports": [
          {
            "name": "http",
            "value": 80,
            "type": "SYSTEM"
          }
        ],
        "eolinkerIDs": [],
        "webhook": {
          "url": ""
        },
        "options": {
          "isCoreApp": false,
          "onlyDeployTag": false,
          "addSysctlKeepalive": false,
          "skyWalkingAgent": false,
          "appLogToKafka": true,
          "buildUseRuntimeJDK": false,
          "jvmGcLog": true
        },
        "partnerApps": [],
        "exclusiveApps": [],
        "preStopWebhook": "",
        "preStopRetainSeconds": 20,
        "remark": ""
      },
      "checkVersionOfDedicatedCloud": []
    }
  k8s1: |-
    apiVersion: v1
    kind: Config
    clusters:
      - name: k8s1
        cluster:
          certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJS3pTdHdzaVVxdHd3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TkRBNU1UUXhNak15TWpoYUZ3MHpOREE1TVRJeE1qTTNNamhhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUN6OUtlTC9DY005SjZEU2hiZUZBb0xFZjN3YXdpOVlmMGpCZHVlV1YzSHJ3UmRObjNvMWdBL1RGMG4KMmcwYUdYckhuY0JSQlY2MHVEWGpvY0p3d0padHF5c3BXa2hEaGtFRzZ5UkpWd3ZtZ214dFgxa0k1M1BBa0hscwo5Q29EZ3hvaUVFQVZHQTU5SHJYZFlmVnFjUzN2L0x6WHdaYjQ2SWhLbXc2OVlBaGhmNFNJYlZNQXlYcTE1NFB0CmNQSnZpQllGNFM0VW9BbzFzdEZGdzJSWjJ4S1NJYi9VOFZtNjJzWGg1bjV5Qk9wVHAycjhZR2dkRVJ2N3hwZzYKY0d3aG5rUXJXN3BpM1hBTENWc2czMkZRZ1ZFZk02ZmpBR0lTMjJxMzkzV2pGcWNFOUt3WmxzNktvUlRjaUhyZwpyY3Ruazk4cUYzVVNNU25wa0hmcjlISHZiNm5SQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJRblR0OVdWSlorc0hWN1o2UG1OV1I4ckcxYWxUQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQUd1dENncjExUQpxblpMQldGYjd5ZEdseEkwaEsvcDVmeHgxSi9FUFBWbkZOek1TcEhHdFpwSDFBcFQ3S0JBUGZvdVJkYXg1bDlUCi92Q3pxNFRmK0J6U3huRzBSSGxYcW9SajNpRFIreGp1dWVQdGJ4VlQxQ0VDS1pJTlhPRHlpUUNhSk9Ock9ac1AKOGxKdEd6SnRqSitJQmFLV2RCS3ZXOXlITlh6V2Y3bmk5UFV5WnZmclhxSC84ak50c2xudGVGYjMvZHZYU3JlbAprYWhjTmVxeXE2c2d3VFRRbmdjQTlHc2paSXRGU1hLS1VEZ0xmU2JlSmswOG5kS0pqTU4xVmkvR05JYmw1RUl4CjVncm5Wd2thaDArbmZNb1pmWVA1LzhSQWpKZUpDa0gyWFpoSCtFTWRvaDdaanNKYWRJTG44Yk9sMTR5Ri9JQ20KZE5Ob1dzVWlTVFVBCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
          server: https://firstshare-k8s1.firstshare.cn:6443
    contexts:
      - name: fs-k8s-app-manager@k8s1
        context:
          cluster: k8s1
          namespace: default
          user: fs-k8s-app-manager@k8s1@user
    users:
      - name: fs-k8s-app-manager@k8s1@user
        user:
          token: **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    current-context: fs-k8s-app-manager@k8s1