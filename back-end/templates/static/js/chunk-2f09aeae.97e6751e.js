(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2f09aeae"],{"11e9":function(t,e,a){var n=a("52a7"),r=a("4630"),l=a("6821"),o=a("6a99"),i=a("69a8"),s=a("c69a"),c=Object.getOwnPropertyDescriptor;e.f=a("9e1e")?c:function(t,e){if(t=l(t),e=o(e,!0),s)try{return c(t,e)}catch(a){}if(i(t,e))return r(!n.f.call(t,e),t[e])}},"1e42":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},r=[],l=(a("a481"),a("25ca")),o=a("21a6"),i=a.n(o),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=l["a"].table_to_book(t,{raw:!0}),a=l["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var n=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";i.a.saveAs(new Blob([a],{type:"application/octet-stream"}),n)}catch(r){this.$message.error("导出失败, err: "+r.message),console.error(r)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,u=a("2877"),p=Object(u["a"])(c,n,r,!1,null,null,null);e["a"]=p.exports},"2fdb":function(t,e,a){"use strict";var n=a("5ca1"),r=a("d2c8"),l="includes";n(n.P+n.F*a("5147")(l),"String",{includes:function(t){return!!~r(this,t,l).indexOf(t,arguments.length>1?arguments[1]:void 0)}})},"4f7f":function(t,e,a){"use strict";var n=a("c26b"),r=a("b39a"),l="Set";t.exports=a("e0b8")(l,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return n.def(r(this,l),t=0===t?0:t,t)}},n)},5147:function(t,e,a){var n=a("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(a){try{return e[n]=!1,!"/./"[t](e)}catch(r){}}return!0}},"51a9":function(t,e,a){"use strict";a.d(e,"c",(function(){return r})),a.d(e,"e",(function(){return l})),a.d(e,"d",(function(){return o})),a.d(e,"l",(function(){return i})),a.d(e,"m",(function(){return s})),a.d(e,"a",(function(){return c})),a.d(e,"f",(function(){return u})),a.d(e,"i",(function(){return p})),a.d(e,"j",(function(){return f})),a.d(e,"k",(function(){return d})),a.d(e,"n",(function(){return m})),a.d(e,"g",(function(){return b})),a.d(e,"b",(function(){return h})),a.d(e,"h",(function(){return v})),a.d(e,"o",(function(){return g}));var n=a("b775");function r(t){return Object(n["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function l(t){return Object(n["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function o(t,e,a){return Object(n["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:a}})}function i(t){return Object(n["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function s(t){return Object(n["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function c(){return Object(n["a"])({url:"/v1/pipeline/all",method:"get"})}function u(t){return Object(n["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function p(t){return Object(n["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function f(t){return Object(n["a"])({url:"/v1/pipeline",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function m(t){return Object(n["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function b(t,e,a,r){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:a,targetNamespace:r}})}function h(t){return Object(n["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function v(t,e,a,r){return Object(n["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:a,targetNamespace:r}})}function g(t){return Object(n["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"530d":function(t,e,a){"use strict";a.d(e,"d",(function(){return r})),a.d(e,"e",(function(){return l})),a.d(e,"a",(function(){return o})),a.d(e,"b",(function(){return i})),a.d(e,"c",(function(){return s}));var n=a("b775");function r(){return Object(n["a"])({url:"/v1/artifact/all",method:"get"})}function l(t){return Object(n["a"])({url:"/v1/artifact/search",method:"get",params:t})}function o(t){return Object(n["a"])({url:"/v1/artifact/analysis",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/v1/artifact",method:"post",data:t})}function s(t){return Object(n["a"])({url:"/v1/artifact",method:"delete",params:{id:t}})}},"55d7":function(t,e,a){"use strict";var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"env-selector-wrapper"},[t.showCluster?a("el-form-item",{attrs:{label:t.clusterLabel,prop:"cluster"}},[a("el-select",{attrs:{placeholder:"选择k8s集群",filterable:""},on:{change:t.clusterChange},model:{value:t.cluster,callback:function(e){t.cluster=e},expression:"cluster"}},t._l(this.clusterOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name+" ("+t.description+")",value:t.name}})})),1)],1):t._e(),t._v(" "),t.showNamespace?a("el-form-item",{attrs:{label:t.namespaceLabel,prop:"namespace"}},[a("el-select",{attrs:{placeholder:"选择namespace"},model:{value:t.namespace,callback:function(e){t.namespace=e},expression:"namespace"}},[t.showAllNamespaces?a("el-option",{key:"*",attrs:{label:"所有",value:""}}):t._e(),t._v(" "),t._l(this.namespaceOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})}))],2)],1):t._e()],1)},r=[],l=a("2d63"),o=(a("7f7f"),{name:"EnvSelector",props:{showAllNamespaces:{type:Boolean,default:!1},showCluster:{type:Boolean,default:!0},showNamespace:{type:Boolean,default:!0},clusterLabel:{type:String,default:"k8s集群"},namespaceLabel:{type:String,default:"运行环境"}},data:function(){return{cluster:"",namespace:""}},mounted:function(){!this.cluster&&this.clusterOptions&&this.clusterOptions.length&&(this.cluster=this.clusterOptions[0].name),this.cluster&&!this.namespace&&this.namespaceOptions&&this.namespaceOptions.length&&(this.namespace=this.namespaceOptions[0])},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){if(this.cluster){var t,e=Object(l["a"])(this.$settings.clusters);try{for(e.s();!(t=e.n()).done;){var a=t.value;if(this.cluster===a.name)return a.namespaces}}catch(n){e.e(n)}finally{e.f()}}return[]}},methods:{clusterChange:function(){this.namespace=""}}}),i=o,s=(a("e0fe"),a("2877")),c=Object(s["a"])(i,n,r,!1,null,null,null);e["a"]=c.exports},"5dbc":function(t,e,a){var n=a("d3f4"),r=a("8b97").set;t.exports=function(t,e,a){var l,o=e.constructor;return o!==a&&"function"==typeof o&&(l=o.prototype)!==a.prototype&&n(l)&&r&&r(t,l),t}},"5df3":function(t,e,a){"use strict";var n=a("02f4")(!0);a("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,a=this._i;return a>=e.length?{value:void 0,done:!0}:(t=n(e,a),this._i+=t.length,{value:t,done:!1})}))},6738:function(t,e,a){},6762:function(t,e,a){"use strict";var n=a("5ca1"),r=a("c366")(!0);n(n.P,"Array",{includes:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}}),a("9c6c")("includes")},"67ab":function(t,e,a){var n=a("ca5a")("meta"),r=a("d3f4"),l=a("69a8"),o=a("86cc").f,i=0,s=Object.isExtensible||function(){return!0},c=!a("79e5")((function(){return s(Object.preventExtensions({}))})),u=function(t){o(t,n,{value:{i:"O"+ ++i,w:{}}})},p=function(t,e){if(!r(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!l(t,n)){if(!s(t))return"F";if(!e)return"E";u(t)}return t[n].i},f=function(t,e){if(!l(t,n)){if(!s(t))return!0;if(!e)return!1;u(t)}return t[n].w},d=function(t){return c&&m.NEED&&s(t)&&!l(t,n)&&u(t),t},m=t.exports={KEY:n,NEED:!1,fastKey:p,getWeak:f,onFreeze:d}},8504:function(t,e,a){"use strict";a.d(e,"g",(function(){return r})),a.d(e,"a",(function(){return l})),a.d(e,"h",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"b",(function(){return s})),a.d(e,"i",(function(){return c})),a.d(e,"d",(function(){return u})),a.d(e,"f",(function(){return p})),a.d(e,"e",(function(){return f}));var n=a("b775");function r(t,e){return Object(n["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function l(t,e,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:a}})}function o(t){return Object(n["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function i(t,e,a){return Object(n["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:a}})}function s(t){return Object(n["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function c(t){return Object(n["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function u(t,e,a){return Object(n["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:a}})}function p(t){return Object(n["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function f(t,e,a,r,l){return Object(n["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:a,revision:r,deployTag:l||""}})}},"8b97":function(t,e,a){var n=a("d3f4"),r=a("cb7c"),l=function(t,e){if(r(t),!n(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,n){try{n=a("9b43")(Function.call,a("11e9").f(Object.prototype,"__proto__").set,2),n(t,[]),e=!(t instanceof Array)}catch(r){e=!0}return function(t,a){return l(t,a),e?t.__proto__=a:n(t,a),t}}({},!1):void 0),check:l}},aae3:function(t,e,a){var n=a("d3f4"),r=a("2d95"),l=a("2b4c")("match");t.exports=function(t){var e;return n(t)&&(void 0!==(e=t[l])?!!e:"RegExp"==r(t))}},b39a:function(t,e,a){var n=a("d3f4");t.exports=function(t,e){if(!n(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},b562:function(t,e,a){"use strict";a.d(e,"o",(function(){return r})),a.d(e,"b",(function(){return l})),a.d(e,"a",(function(){return o})),a.d(e,"k",(function(){return i})),a.d(e,"i",(function(){return s})),a.d(e,"d",(function(){return c})),a.d(e,"h",(function(){return u})),a.d(e,"g",(function(){return p})),a.d(e,"l",(function(){return f})),a.d(e,"n",(function(){return d})),a.d(e,"f",(function(){return m})),a.d(e,"e",(function(){return b})),a.d(e,"c",(function(){return h})),a.d(e,"j",(function(){return v})),a.d(e,"q",(function(){return g})),a.d(e,"m",(function(){return y})),a.d(e,"p",(function(){return _}));var n=a("b775");function r(t){return Object(n["a"])({url:"/v1/app/search",method:"get",params:t})}function l(){return Object(n["a"])({url:"/v1/app/apps-with-env",method:"get"})}function o(){return Object(n["a"])({url:"/v1/app/all",method:"get"})}function i(){return Object(n["a"])({url:"/v1/app/names",method:"get"})}function s(t){return Object(n["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function c(t){return Object(n["a"])({url:"/v1/app",method:"post",data:t})}function u(t){return Object(n["a"])({url:"/v1/app",method:"put",data:t})}function p(t){return Object(n["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function f(t,e,a){return Object(n["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:a}})}function d(t){return Object(n["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function m(t){return Object(n["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function h(t){return Object(n["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function v(t,e){return Object(n["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function g(t,e){return Object(n["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function y(t,e){return Object(n["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function _(){return Object(n["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}},c26b:function(t,e,a){"use strict";var n=a("86cc").f,r=a("2aeb"),l=a("dcbc"),o=a("9b43"),i=a("f605"),s=a("4a59"),c=a("01f9"),u=a("d53b"),p=a("7a56"),f=a("9e1e"),d=a("67ab").fastKey,m=a("b39a"),b=f?"_s":"size",h=function(t,e){var a,n=d(e);if("F"!==n)return t._i[n];for(a=t._f;a;a=a.n)if(a.k==e)return a};t.exports={getConstructor:function(t,e,a,c){var u=t((function(t,n){i(t,u,e,"_i"),t._t=e,t._i=r(null),t._f=void 0,t._l=void 0,t[b]=0,void 0!=n&&s(n,a,t[c],t)}));return l(u.prototype,{clear:function(){for(var t=m(this,e),a=t._i,n=t._f;n;n=n.n)n.r=!0,n.p&&(n.p=n.p.n=void 0),delete a[n.i];t._f=t._l=void 0,t[b]=0},delete:function(t){var a=m(this,e),n=h(a,t);if(n){var r=n.n,l=n.p;delete a._i[n.i],n.r=!0,l&&(l.n=r),r&&(r.p=l),a._f==n&&(a._f=r),a._l==n&&(a._l=l),a[b]--}return!!n},forEach:function(t){m(this,e);var a,n=o(t,arguments.length>1?arguments[1]:void 0,3);while(a=a?a.n:this._f){n(a.v,a.k,this);while(a&&a.r)a=a.p}},has:function(t){return!!h(m(this,e),t)}}),f&&n(u.prototype,"size",{get:function(){return m(this,e)[b]}}),u},def:function(t,e,a){var n,r,l=h(t,e);return l?l.v=a:(t._l=l={i:r=d(e,!0),k:e,v:a,p:n=t._l,n:void 0,r:!1},t._f||(t._f=l),n&&(n.n=l),t[b]++,"F"!==r&&(t._i[r]=l)),t},getEntry:h,setStrong:function(t,e,a){c(t,e,(function(t,a){this._t=m(t,e),this._k=a,this._l=void 0}),(function(){var t=this,e=t._k,a=t._l;while(a&&a.r)a=a.p;return t._t&&(t._l=a=a?a.n:t._t._f)?u(0,"keys"==e?a.k:"values"==e?a.v:[a.k,a.v]):(t._t=void 0,u(1))}),a?"entries":"values",!a,!0),p(e)}}},d2c8:function(t,e,a){var n=a("aae3"),r=a("be13");t.exports=function(t,e,a){if(n(e))throw TypeError("String#"+a+" doesn't accept regex!");return String(r(t))}},e0b8:function(t,e,a){"use strict";var n=a("7726"),r=a("5ca1"),l=a("2aba"),o=a("dcbc"),i=a("67ab"),s=a("4a59"),c=a("f605"),u=a("d3f4"),p=a("79e5"),f=a("5cc5"),d=a("7f20"),m=a("5dbc");t.exports=function(t,e,a,b,h,v){var g=n[t],y=g,_=h?"set":"add",O=y&&y.prototype,x={},w=function(t){var e=O[t];l(O,t,"delete"==t||"has"==t?function(t){return!(v&&!u(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return v&&!u(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,a){return e.call(this,0===t?0:t,a),this})};if("function"==typeof y&&(v||O.forEach&&!p((function(){(new y).entries().next()})))){var k=new y,j=k[_](v?{}:-0,1)!=k,F=p((function(){k.has(1)})),S=f((function(t){new y(t)})),D=!v&&p((function(){var t=new y,e=5;while(e--)t[_](e,e);return!t.has(-0)}));S||(y=e((function(e,a){c(e,y,t);var n=m(new g,e,y);return void 0!=a&&s(a,h,n[_],n),n})),y.prototype=O,O.constructor=y),(F||D)&&(w("delete"),w("has"),h&&w("get")),(D||j)&&w(_),v&&O.clear&&delete O.clear}else y=b.getConstructor(e,t,h,_),o(y.prototype,a),i.NEED=!0;return d(y,t),x[t]=y,r(r.G+r.W+r.F*(y!=g),x),v||b.setStrong(y,t,h),y}},e0fe:function(t,e,a){"use strict";a("6738")},fb32:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{padding:"5px 10px"}},[a("el-tabs",{model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{label:"发布流程分析",name:"analysis-pipeline",lazy:!0}},[a("pipeline-analysis")],1),t._v(" "),a("el-tab-pane",{attrs:{label:"发布流程实例数（所有云）",name:"analysis-pipeline-replicas",lazy:!0}},[a("pipeline-replicas")],1),t._v(" "),a("el-tab-pane",{attrs:{label:"部署模块分析",name:"analysis-artifact",lazy:!0}},[a("artifact-analysis")],1)],1)],1)},r=[],l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.searchForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{filterable:"",size:"small"},model:{value:t.searchForm.status,callback:function(e){t.$set(t.searchForm,"status",e)},expression:"searchForm.status"}},[a("el-option",{attrs:{label:"所有",value:""}}),t._v(" "),t._l(this.statusOptions,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})}))],2)],1),t._v(" "),a("el-form-item",{attrs:{label:"集群"}},[a("el-select",{attrs:{filterable:"",size:"small"},model:{value:t.searchForm.cluster,callback:function(e){t.$set(t.searchForm,"cluster",e)},expression:"searchForm.cluster"}},[a("el-option",{attrs:{label:"所有",value:""}}),t._v(" "),t._l(this.clusterOptions,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})}))],2)],1),t._v(" "),a("el-form-item",{attrs:{label:"环境"}},[a("el-select",{attrs:{filterable:"",size:"small"},model:{value:t.searchForm.namespace,callback:function(e){t.$set(t.searchForm,"namespace",e)},expression:"searchForm.namespace"}},[a("el-option",{attrs:{label:"所有",value:""}}),t._v(" "),t._l(this.namespaceOptions,(function(t){return a("el-option",{key:t,attrs:{label:t,value:t}})}))],2)],1),t._v(" "),a("el-form-item",{attrs:{label:"应用"}},[a("el-input",{attrs:{placeholder:"支持模糊匹配",size:"small",clearable:""},model:{value:t.searchForm.app,callback:function(e){t.$set(t.searchForm,"app","string"===typeof e?e.trim():e)},expression:"searchForm.app"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"镜像"}},[a("el-input",{attrs:{placeholder:"支持模糊匹配",size:"small",clearable:""},model:{value:t.searchForm.image,callback:function(e){t.$set(t.searchForm,"image","string"===typeof e?e.trim():e)},expression:"searchForm.image"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"JVM参数"}},[a("el-input",{attrs:{placeholder:"支持模糊匹配",size:"small",clearable:""},model:{value:t.searchForm.javaOpts,callback:function(e){t.$set(t.searchForm,"javaOpts","string"===typeof e?e.trim():e)},expression:"searchForm.javaOpts"}})],1),t._v(" "),a("el-form-item",{staticStyle:{display:"none"},attrs:{label:""}},[a("el-checkbox-group",{on:{change:t.filterByOptions},model:{value:t.filterOptions,callback:function(e){t.filterOptions=e},expression:"filterOptions"}},[a("el-checkbox",{staticStyle:{"margin-right":"10px"},attrs:{label:"isCoreApp"}},[t._v("核心服务")]),t._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},attrs:{label:"appLogToKafka"}},[t._v("开启了日志上报")]),t._v(" "),a("el-checkbox",{staticStyle:{"margin-right":"10px"},attrs:{label:"jvmGcLog"}},[t._v("开启了JmvGC日志")])],1)],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"small"},on:{click:t.loadTableData}},[t._v("查询")]),t._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),t._v(" "),a("el-pagination",{staticStyle:{display:"inline-block"},attrs:{"current-page":t.searchForm.page,"page-sizes":[20,50,100,200,400,1e3,2e3,5e3],"page-size":t.searchForm.limit,layout:"total,prev,pager,next,sizes",total:t.tableDataByFilter.count},on:{"size-change":t.pageSizeChange,"current-change":t.pageChange}}),t._v(" "),a("el-button",{staticStyle:{display:"inline-block",color:"#999"},attrs:{type:"text",size:"small"},on:{click:t.loadRunningPodNum}},[t._v("加载运行副本")]),t._v(" "),a("div",{staticStyle:{display:"inline-block","margin-left":"50px","font-size":"12px",color:"orangered"}},[t._v("\n    资源统计： "+t._s(this.resourceTotal)+"\n  ")]),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:t.tableDataByFilter.data,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),t._v(" "),a("el-table-column",{attrs:{label:"状态",prop:"status",width:"80"}}),t._v(" "),a("el-table-column",{attrs:{label:"等级",sortable:"",prop:"appLevel",width:"80px"}}),t._v(" "),a("el-table-column",{attrs:{label:"集群",sortable:"",prop:"cluster",width:"100px"}}),t._v(" "),a("el-table-column",{attrs:{label:"运行环境",sortable:"",prop:"namespace"}}),t._v(" "),a("el-table-column",{attrs:{label:"镜像",sortable:"",prop:"baseImage"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.baseImage.substring(e.row.baseImage.lastIndexOf("/")+1)))])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"资源池",sortable:"",prop:"schedule.node"}}),t._v(" "),a("el-table-column",{attrs:{label:"开关"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.options,(function(e,n){return a("div",[e?a("el-tag",{staticStyle:{padding:"1px",margin:"1px 0","line-height":"normal",height:"unset","font-size":"10px"}},[t._v(t._s(n))]):t._e()],1)}))}}])}),t._v(" "),a("el-table-column",{attrs:{label:"副本",sortable:"",width:"80px",prop:"replicas",align:"center"}}),t._v(" "),t.showRunningPod?a("el-table-column",{attrs:{label:"运行Pod",width:"80px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.extraAttr.runningPodNum))])]}}],null,!1,1527458219)}):t._e(),t._v(" "),a("el-table-column",{attrs:{label:"JVM参数",width:"200px",prop:"jvmOpts","show-overflow-tooltip":""}}),t._v(" "),a("el-table-column",{attrs:{label:"CPU",width:"90px",sortable:"",prop:"resources.limitCPU"},scopedSlots:t._u([{key:"default",fn:function(e){return e.row.resources?[t._v("\n        "+t._s(e.row.resources.requestCPU.toFixed(1))+" - "+t._s(e.row.resources.limitCPU.toFixed(1))+"\n      ")]:void 0}}],null,!0)}),t._v(" "),a("el-table-column",{attrs:{label:"内存 (MB)",width:"120px",sortable:"",prop:"resources.limitMemory"},scopedSlots:t._u([{key:"default",fn:function(e){return e.row.resources?[t._v("\n        "+t._s(e.row.resources.requestMemory)+" - "+t._s(e.row.resources.limitMemory)+"\n      ")]:void 0}}],null,!0)}),t._v(" "),a("el-table-column",{attrs:{label:"负责人",sortable:"",prop:"appOwners"}}),t._v(" "),a("el-table-column",{attrs:{label:"",width:"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("router-link",{attrs:{to:{name:"app-pipeline-edit",query:{pipelineId:e.row.id}},target:"_blank"}},[a("span",{staticStyle:{color:"#409EFF","font-weight":"500"}},[t._v("编辑")])]),t._v(" "),a("router-link",{staticStyle:{"margin-left":"2px"},attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[a("span",{staticStyle:{color:"#409EFF","font-weight":"500"}},[t._v("发布流程")])]),t._v(" "),a("br"),t._v(" "),a("el-button",{staticStyle:{color:"#fc8133"},attrs:{type:"text"},on:{click:function(a){return t.removePipe(e.row)}}},[t._v("下线")]),t._v(" "),a("router-link",{staticStyle:{"margin-left":"2px"},attrs:{to:{name:"pod-index",query:{cluster:e.row.cluster,namespace:e.row.namespace,app:e.row.app}},target:"_blank"}},[a("span",{staticStyle:{color:"#409EFF","font-weight":"500"}},[t._v("实例管理")])])]}}])})],1)],1)},o=[],i=(a("7f7f"),a("2fdb"),a("6762"),a("2d63")),s=a("51a9"),c=a("1e42"),u=a("b562"),p=a("8504"),f={name:"pipelineAnalysis",data:function(){return{appOwners:{},appLevels:{},tableData:{count:0,data:[]},statusOptions:[{label:"可用",value:"enabled"},{label:"禁用",value:"disabled"},{label:"已迁移",value:"migrated"}],tableDataByFilter:{count:0,data:[]},filterOptions:[],tableLoading:!1,showRunningPod:!1,searchForm:{cluster:"",namespace:"",app:"",image:"",status:"",javaOpts:"",options:"",page:1,limit:20}}},components:{ExportButton:c["a"]},computed:{clusterOptions:function(){return this.$settings.clusters},namespaceOptions:function(){var t,e=[],a=Object(i["a"])(this.$settings.clusters);try{for(a.s();!(t=a.n()).done;){var n,r=t.value,l=Object(i["a"])(r.namespaces);try{for(l.s();!(n=l.n()).done;){var o=n.value;e.includes(o)||e.push(o)}}catch(s){l.e(s)}finally{l.f()}}}catch(s){a.e(s)}finally{a.f()}return e},resourceTotal:function(){if(!this.tableDataByFilter||!this.tableDataByFilter.data||0===this.tableDataByFilter.data.length)return"--";var t,e=0,a=0,n=0,r=0,l=Object(i["a"])(this.tableDataByFilter.data);try{for(l.s();!(t=l.n()).done;){var o=t.value;o.resources&&(e+=o.resources.requestCPU*o.replicas,a+=o.resources.limitCPU*o.replicas,n+=o.resources.requestMemory*o.replicas,r+=o.resources.limitMemory*o.replicas)}}catch(s){l.e(s)}finally{l.f()}return"CPU: ".concat(e.toFixed(1)+" - "+a.toFixed(1)," | 内存: ").concat(n+" - "+r)}},mounted:function(){var t=this.$route.query.namespace;t&&(this.searchForm.namespace=t);var e=this.$route.query.keyword;e&&(this.searchForm.keyword=e),this.loadApps(this.loadTableData)},methods:{loadTableData:function(){var t=this;this.searchForm.limit>=200&&this.$message.warning("查询数据比较多时，页面渲染时间比较慢，请耐心等候..."),this.tableLoading=!0,Object(s["l"])(this.searchForm).then((function(e){var a,n=e.data.data,r=Object(i["a"])(n);try{for(r.s();!(a=r.n()).done;){var l=a.value;l.appOwners=t.appOwners[l.app]||"-",l.appLevel=t.appLevels[l.app]||"-"}}catch(o){r.e(o)}finally{r.f()}t.tableData=e.data,t.showRunningPod=!1,t.filterByOptions()})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},loadRunningPodNum:function(){if(this.tableDataByFilter.data.length>50)this.$message.warning("数据超过50， 请下调分页数");else{var t,e=Object(i["a"])(this.tableDataByFilter.data);try{for(e.s();!(t=e.n()).done;){var a=t.value;this.findDeployment(a.cluster,a.namespace,a.app)}}catch(n){e.e(n)}finally{e.f()}this.showRunningPod=!0}},findDeployment:function(t,e,a){var n,r=this,l=null,o=Object(i["a"])(this.tableDataByFilter.data);try{for(o.s();!(n=o.n()).done;){var s=n.value;if(s.cluster===t&&s.namespace===e&&s.app===a){l=s;break}}}catch(c){o.e(c)}finally{o.f()}null!==l&&(l.extraAttr.runningPodNum="--",Object(p["a"])(t,e,a).then((function(t){l.extraAttr.runningPodNum=t.data.replicas})).catch((function(t){r.$message.error(t.message)})))},pageChange:function(t){this.searchForm.page=t,this.loadTableData()},pageSizeChange:function(t){this.searchForm.limit=t,this.loadTableData()},pipelinePage:function(t){this.$router.push({name:"cicd-app-deploy",query:{app:t.app}})},filterByOptions:function(){var t=this;if(!this.filterOptions||0===this.filterOptions.length)return this.tableDataByFilter.data=this.tableData.data,void(this.tableDataByFilter.count=this.tableData.count);this.tableDataByFilter.data=this.tableData.data.filter((function(e){for(var a in e.options)if(t.filterOptions.includes(a)&&e.options[a])return!0;return!1})),this.tableDataByFilter.count=this.tableData.count},loadApps:function(t){var e=this;this.tableLoading=!0,Object(u["o"])({keyword:"",page:1,limit:2e3}).then((function(t){var a,n=Object(i["a"])(t.data.data);try{for(n.s();!(a=n.n()).done;){var r=a.value;e.appOwners[r.name]=r.mainOwner,e.appLevels[r.name]=r.level}}catch(l){n.e(l)}finally{n.f()}})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.tableLoading=!1,t&&t()}))},removePipe:function(t){var e=this;this.$prompt("请输入应用名","下线确认提示",{confirmButtonText:"继续下线",confirmButtonClass:"el-button--danger",cancelButtonText:"取消",inputValue:t.app}).then((function(a){var n=a.value;n===t.app?Object(s["k"])(t.id).then((function(a){e.$message.success("操作成功"),e.tableDataByFilter.data=e.tableDataByFilter.data.filter((function(e){return e.id!==t.id})),e.tableDataByFilter.count=e.tableDataByFilter.data.length})).catch((function(t){e.$message.error(t.message)})).finally((function(){})):e.$message.info("应用名输入错误")})).catch((function(){console.log("取消下线确认操作")}))}}},d=f,m=a("2877"),b=Object(m["a"])(d,l,o,!1,null,null,null),h=b.exports,v=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("div",[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.searchForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("env-selector",{ref:"nsSelector",attrs:{"show-all-namespaces":!0}}),t._v(" "),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTableData}},[t._v("查询")]),t._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1)],1)],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:t.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"Git地址",sortable:"",prop:"gitUrl"}}),t._v(" "),a("el-table-column",{attrs:{label:"模块",sortable:"",prop:"module"}}),t._v(" "),a("el-table-column",{attrs:{label:"描述",prop:"remark"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建人",prop:"author"}}),t._v(" "),a("el-table-column",{attrs:{label:"被引用数",prop:"extraAttr.refCount",sortable:"",width:"120"}}),t._v(" "),a("el-table-column",{attrs:{label:"引用发布流程",width:"560"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.extraAttr.ref,(function(e){return a("div",[t._v("\n            "+t._s(e)+"\n          ")])}))}}])})],1)],1)},g=[],y=a("530d"),_=a("55d7"),O={name:"artifactAnalysis",data:function(){return{tableData:[],tableLoading:!1,searchForm:{cluster:"",namespace:""}}},components:{ExportButton:c["a"],EnvSelector:_["a"]},computed:{},mounted:function(){},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,this.searchForm.cluster=this.$refs.nsSelector.cluster,this.searchForm.namespace=this.$refs.nsSelector.namespace,Object(y["a"])(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},pipelinePage:function(t){this.$router.push({name:"cicd-app-deploy",query:{app:t.app}})},podPage:function(t){var e={cluster:t.cluster,namespace:t.namespace,app:t.app};this.$router.push({name:"pod-index",query:e})}}},x=O,w=Object(m["a"])(x,v,g,!1,null,null,null),k=w.exports,j=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.search}},[t._v("查询")]),t._v(" "),a("export-button",{attrs:{"table-ref":this.$refs.table001}})],1),t._v(" "),a("el-card",{staticClass:"box-card"},[a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{"max-height":"700",data:t.tableData,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app",width:"240px;"}}),t._v(" "),t._l(this.tableColumns,(function(e,n){return a("el-table-column",{attrs:{label:e,sortable:"",prop:e},scopedSlots:t._u([{key:"default",fn:function(n){return[n.row[e]>0?a("div",{staticStyle:{color:"dodgerblue","font-weight":"bold"}},[t._v(t._s(n.row[e]))]):t._e()]}}],null,!0)})})),t._v(" "),a("el-table-column",{attrs:{label:"",fixed:"right",width:"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return t.pipelinePage(e.row)}}},[t._v("发布流程页\n            ")])]}}])})],2)],1)])],1)},F=[],S=(a("ac6a"),a("5df3"),a("4f7f"),{name:"pipelineReplicas",data:function(){return{tableColumns:new Set,tableData:[],tableLoading:!1,envType:""}},components:{ExportButton:c["a"]},mounted:function(){},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(s["a"])().then((function(e){t.tableData=t.tableDataHandler(e.data)})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},pipelinePage:function(t){var e=this.$router.resolve({name:"/share",query:{app:t.app}});window.open(e.href,"_blank")},search:function(){this.loadTableData()},tableDataHandler:function(t){this.tableColumns=new Set;var e,a=[],n=Object(i["a"])(t);try{for(n.s();!(e=n.n()).done;){var r=e.value;a.push(r),this.tableColumns.add(r.namespace)}}catch(h){n.e(h)}finally{n.f()}for(var l=[],o=function(){var t=c[s],e=l.filter((function(e){return e["app"]===t.app})),a={app:t.app};e&&e.length>0?a=e[0]:l.push(a),a[t.namespace]=t.replicas},s=0,c=a;s<c.length;s++)o();for(var u=0,p=l;u<p.length;u++){var f,d=p[u],m=Object(i["a"])(this.tableColumns);try{for(m.s();!(f=m.n()).done;){var b=f.value;d[b]||(d[b]=0)}}catch(h){m.e(h)}finally{m.f()}}return l}}}),D=S,$=Object(m["a"])(D,j,F,!1,null,null,null),C=$.exports,E={components:{PipelineReplicas:C,ArtifactAnalysis:k,PipelineAnalysis:h},mounted:function(){},computed:{},data:function(){return{activeTab:"k8s-clusters"}},methods:{}},B=E,L=Object(m["a"])(B,n,r,!1,null,null,null);e["default"]=L.exports}}]);