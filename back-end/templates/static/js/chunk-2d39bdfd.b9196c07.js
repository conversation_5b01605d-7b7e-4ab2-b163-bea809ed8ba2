(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d39bdfd"],{"1e42":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"inline"}},[a("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},n=[],o=(a("a481"),a("25ca")),l=a("21a6"),r=a.n(l),s={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=o["a"].table_to_book(t,{raw:!0}),a=o["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var i=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";r.a.saveAs(new Blob([a],{type:"application/octet-stream"}),i)}catch(n){this.$message.error("导出失败, err: "+n.message),console.error(n)}return a}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=s,d=a("2877"),u=Object(d["a"])(c,i,n,!1,null,null,null);e["a"]=u.exports},2352:function(t,e,a){"use strict";a("a242")},"530d":function(t,e,a){"use strict";a.d(e,"d",(function(){return n})),a.d(e,"e",(function(){return o})),a.d(e,"a",(function(){return l})),a.d(e,"b",(function(){return r})),a.d(e,"c",(function(){return s}));var i=a("b775");function n(){return Object(i["a"])({url:"/v1/artifact/all",method:"get"})}function o(t){return Object(i["a"])({url:"/v1/artifact/search",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/v1/artifact/analysis",method:"get",params:t})}function r(t){return Object(i["a"])({url:"/v1/artifact",method:"post",data:t})}function s(t){return Object(i["a"])({url:"/v1/artifact",method:"delete",params:{id:t}})}},6332:function(t,e,a){"use strict";a("c9c6")},a242:function(t,e,a){},acaa:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",[a("el-row",[a("el-col",{attrs:{span:4}},[a("div",[a("el-button",{attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:t.createPage}},[t._v("新建")])],1)]),t._v(" "),a("el-col",{attrs:{span:20}},[a("div",{staticStyle:{"text-align":"right"}},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"关键字"}},[a("el-input",{staticStyle:{width:"360px"},attrs:{clearable:""},model:{value:t.searchForm.keyword,callback:function(e){t.$set(t.searchForm,"keyword",e)},expression:"searchForm.keyword"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTableData}},[t._v("查询")])],1)],1)],1)])],1)],1),t._v(" "),a("el-pagination",{attrs:{"current-page":t.searchForm.page,"page-size":t.searchForm.limit,layout:"total,prev,pager,next",total:t.tableData.count},on:{"current-change":t.PageChange}}),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableData.data,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"Git地址",sortable:"",prop:"gitUrl"}}),t._v(" "),a("el-table-column",{attrs:{label:"模块",sortable:"",prop:"module"}}),t._v(" "),a("el-table-column",{attrs:{label:"描述",prop:"remark"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建人",width:"120px",prop:"author"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",sortable:"",width:"140px",prop:"createdTime"}}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"160px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-popconfirm",{attrs:{title:"删除部署模块不应影响到应用和发布流程，确定继续删除吗？"},on:{confirm:function(a){return t.deleteRow(e.$index,e.row)}}},[a("el-button",{attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[t._v("删除\n          ")])],1)]}}])})],1),t._v(" "),a("el-dialog",{attrs:{title:"新建",visible:t.dialogEditVisible,width:"50%","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogEditVisible=e}}},[a("el-form",{ref:"dialogEditForm",attrs:{model:t.dialogEditForm,"label-width":"120px",rules:t.dialogEditFormRules}},[a("el-form-item",{attrs:{label:"Git地址",prop:"gitUrl"}},[a("el-input",{model:{value:t.dialogEditForm.gitUrl,callback:function(e){t.$set(t.dialogEditForm,"gitUrl","string"===typeof e?e.trim():e)},expression:"dialogEditForm.gitUrl"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"模块"}},[a("el-input",{model:{value:t.dialogEditForm.module,callback:function(e){t.$set(t.dialogEditForm,"module","string"===typeof e?e.trim():e)},expression:"dialogEditForm.module"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{attrs:{type:"textarea",rows:3},model:{value:t.dialogEditForm.remark,callback:function(e){t.$set(t.dialogEditForm,"remark",e)},expression:"dialogEditForm.remark"}})],1)],1),t._v(" "),a("div",{staticStyle:{"padding-left":"120px","line-height":"1.5em"}},[t._v("\n      【Git地址】: 项目的Git地址，必须为 https 开头"),a("br"),t._v("\n      【模块】: 项目的子模块，如果没有则保留为空\n    ")]),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogEditVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.create()}}},[t._v("确 定")])],1)],1)],1)},n=[],o=(a("aef6"),a("f559"),a("530d")),l={name:"artifactList",data:function(){var t=function(t,e,a){e.startsWith("https://")&&e.endsWith(".git")?a():a(new Error("地址必须为https://xxx.git格式"))};return{searchForm:{keyword:"",page:1,limit:10},tableData:[],tableLoading:!1,dialogEditVisible:!1,dialogEditForm:{id:0,gitUrl:"",module:""},dialogEditFormRules:{gitUrl:[{required:!0,message:"请输入项目Git地址",trigger:"blur"},{validator:t,message:"Git地址必须为https://xxx.git格式",trigger:"blur"}]}}},computed:{},mounted:function(){this.loadTableData()},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(o["e"])(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},resetEditForm:function(){this.dialogEditForm.id=0,this.dialogEditForm.gitUrl="",this.dialogEditForm.module=""},PageChange:function(t){this.searchForm.page=t,this.loadTableData()},createPage:function(){this.dialogEditVisible=!0,this.resetEditForm()},deleteRow:function(t,e){var a=this;Object(o["c"])(e.id).then((function(e){a.tableData.data.splice(t,1)})).catch((function(t){a.$message.error(t.message)}))},create:function(){var t=this;this.$refs["dialogEditForm"].validate((function(e){if(!e)return!1;Object(o["b"])(t.dialogEditForm).then((function(e){t.dialogEditVisible=!1,t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))}))}}},r=l,s=(a("2352"),a("2877")),c=Object(s["a"])(r,i,n,!1,null,"6cf89c18",null);e["default"]=c.exports},aef6:function(t,e,a){"use strict";var i=a("5ca1"),n=a("9def"),o=a("d2c8"),l="endsWith",r=""[l];i(i.P+i.F*a("5147")(l),"String",{endsWith:function(t){var e=o(this,t,l),a=arguments.length>1?arguments[1]:void 0,i=n(e.length),s=void 0===a?i:Math.min(n(a),i),c=String(t);return r?r.call(e,c,s):e.slice(s-c.length,s)===c}})},b562:function(t,e,a){"use strict";a.d(e,"o",(function(){return n})),a.d(e,"b",(function(){return o})),a.d(e,"a",(function(){return l})),a.d(e,"k",(function(){return r})),a.d(e,"i",(function(){return s})),a.d(e,"d",(function(){return c})),a.d(e,"h",(function(){return d})),a.d(e,"g",(function(){return u})),a.d(e,"l",(function(){return m})),a.d(e,"n",(function(){return p})),a.d(e,"f",(function(){return f})),a.d(e,"e",(function(){return g})),a.d(e,"c",(function(){return b})),a.d(e,"j",(function(){return v})),a.d(e,"q",(function(){return h})),a.d(e,"m",(function(){return _})),a.d(e,"p",(function(){return y}));var i=a("b775");function n(t){return Object(i["a"])({url:"/v1/app/search",method:"get",params:t})}function o(){return Object(i["a"])({url:"/v1/app/apps-with-env",method:"get"})}function l(){return Object(i["a"])({url:"/v1/app/all",method:"get"})}function r(){return Object(i["a"])({url:"/v1/app/names",method:"get"})}function s(t){return Object(i["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function c(t){return Object(i["a"])({url:"/v1/app",method:"post",data:t})}function d(t){return Object(i["a"])({url:"/v1/app",method:"put",data:t})}function u(t){return Object(i["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function m(t,e,a){return Object(i["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:a}})}function p(t){return Object(i["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function f(t){return Object(i["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function g(t){return Object(i["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function b(t){return Object(i["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function v(t,e){return Object(i["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function h(t,e){return Object(i["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function _(t,e){return Object(i["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function y(){return Object(i["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}},c356:function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),a.d(e,"b",(function(){return o})),a.d(e,"c",(function(){return l}));var i=a("b775");function n(t){return Object(i["a"])({url:"/operation/app-version-history",method:"get",params:t})}function o(){return Object(i["a"])({url:"/operation/cmdb-owner",method:"get"})}function l(){return Object(i["a"])({url:"/operation/cmdb-sync",method:"get"})}},c9c6:function(t,e,a){},f559:function(t,e,a){"use strict";var i=a("5ca1"),n=a("9def"),o=a("d2c8"),l="startsWith",r=""[l];i(i.P+i.F*a("5147")(l),"String",{startsWith:function(t){var e=o(this,t,l),a=n(Math.min(arguments.length>1?arguments[1]:void 0,e.length)),i=String(t);return r?r.call(e,i,a):e.slice(a,a+i.length)===i}})},f82c:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-tabs",{attrs:{type:"border-card"},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[a("el-tab-pane",{attrs:{name:"app",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"5px"},attrs:{"icon-class":"app"}}),t._v("应用")],1),t._v(" "),a("app-list")],1),t._v(" "),a("el-tab-pane",{attrs:{label:"部署模块",name:"artifact",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"5px"},attrs:{"icon-class":"module2"}}),t._v("部署模块")],1),t._v(" "),a("artifact-list")],1),t._v(" "),a("el-tab-pane",{attrs:{label:"配置中心CMDB",name:"permission",lazy:!0}},[a("span",{attrs:{slot:"label"},slot:"label"},[a("svg-icon",{staticStyle:{"margin-right":"5px"},attrs:{"icon-class":"permission"}}),t._v("配置中心CMDB")],1),t._v(" "),a("cms-cmdb")],1)],1)],1)},n=[],o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.pageLoading,expression:"pageLoading"}]},[a("div",[a("el-row",[a("el-col",{attrs:{span:4}},[a("div",[a("el-button",{attrs:{type:"text",icon:"el-icon-circle-plus-outline"},on:{click:t.createPage}},[t._v("新建")])],1)]),t._v(" "),a("el-col",{attrs:{span:20}},[a("div",{staticStyle:{"text-align":"right"}},[a("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.loadTableData(e)},submit:function(t){t.preventDefault()}}},[a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"服务等级"}},[a("el-select",{staticStyle:{width:"180px"},attrs:{placeholder:"请选择"},model:{value:t.searchForm.level,callback:function(e){t.$set(t.searchForm,"level",e)},expression:"searchForm.level"}},[a("el-option",{attrs:{label:"所有",value:""}}),t._v(" "),a("el-option",{attrs:{label:"L0-底层公共服务",value:"L0"}}),t._v(" "),a("el-option",{attrs:{label:"L1-核心业务服务",value:"L1"}}),t._v(" "),a("el-option",{attrs:{label:"L2-一般业务服务",value:"L2"}}),t._v(" "),a("el-option",{attrs:{label:"L3-非业务服务",value:"L3"}})],1)],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"部门"}},[a("el-input",{staticStyle:{width:"220px"},attrs:{clearable:""},model:{value:t.searchForm.department,callback:function(e){t.$set(t.searchForm,"department",e)},expression:"searchForm.department"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"关键字"}},[a("el-input",{staticStyle:{width:"300px"},attrs:{clearable:""},model:{value:t.searchForm.keyword,callback:function(e){t.$set(t.searchForm,"keyword",e)},expression:"searchForm.keyword"}})],1),t._v(" "),a("el-form-item",{staticStyle:{"margin-bottom":"0"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.loadTableData}},[t._v("查询")])],1)],1)],1)])],1)],1),t._v(" "),t._m(0),t._v(" "),a("div",[a("div",{staticStyle:{display:"inline-block"}},[a("el-pagination",{attrs:{"current-page":t.searchForm.page,"page-size":t.searchForm.limit,"page-sizes":[10,20,50,100,200,500,1e3],layout:"total,prev,pager,next,sizes",total:t.tableData.count},on:{"size-change":t.pageSizeChange,"current-change":t.PageChange}})],1),t._v(" "),t.tableData.data&&t.tableData.data.length>0?a("export-button",{attrs:{"table-ref":this.$refs.table001}}):t._e(),t._v(" "),a("el-button",{staticStyle:{color:"#888","font-size":"12px","margin-left":"50px"},attrs:{type:"text",icon:"el-icon-refresh"},on:{click:t.syncAppFromCRM}},[t._v("手动同步数据（从配置中心到发布系统）")])],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:t.tableData.data,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":""}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{label:"应用名",prop:"name"}}),t._v(" "),a("el-table-column",{attrs:{label:"服务等级",prop:"level",width:"100",align:"center"}}),t._v(" "),a("el-table-column",{attrs:{label:"描述",prop:"remark"}}),t._v(" "),a("el-table-column",{attrs:{label:"所属部门",prop:"department"}}),t._v(" "),a("el-table-column",{attrs:{label:"创建时间",width:"100px",prop:"createdTime"}}),t._v(" "),a("el-table-column",{attrs:{label:"发版权限",align:"center",width:"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.orgs&&e.row.orgs.length>0?[a("i",{staticClass:"el-icon-circle-check",staticStyle:{color:"#67c23a","font-weight":"bold","font-size":"20px"}}),a("br"),t._v(" "),a("el-tooltip",{attrs:{effect:"dark",content:"部门："+e.row.orgs.join(","),placement:"top"}},[a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"}},[t._v("查看")])],1)]:[t._v("\n          --"),a("br")],t._v(" "),a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(a){return t.permPage(e.row)}}},[t._v("修改\n        ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"发布时间窗口",align:"center","show-overflow-tooltip":"",width:"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.timeWindow&&e.row.timeWindow.length>0?[a("i",{staticClass:"el-icon-circle-check",staticStyle:{color:"#67c23a","font-weight":"bold","font-size":"20px"}}),a("br"),t._v(" "),a("el-tooltip",{attrs:{effect:"dark",placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("pre",[t._v(t._s(e.row.timeWindowDesc))])]),t._v(" "),a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"}},[t._v("查看")])],1)]:[t._v("\n          --"),a("br")],t._v(" "),a("el-button",{staticStyle:{"font-size":"12px",padding:"0"},attrs:{type:"text"},on:{click:function(a){return t.editPage(e.row.name)}}},[t._v("修改\n        ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"第一负责人",prop:"mainOwner"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{"font-size":"12px"}},[t._v("\n          "+t._s(e.row.mainOwner)+"\n        ")])]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"负责人",prop:"owners"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.owners&&e.row.owners.length>0?a("div",{staticStyle:{"font-size":"12px"}},[t._v("\n          "+t._s(e.row.owners.join(","))),a("br")]):t._e()]}}])}),t._v(" "),a("el-table-column",{attrs:{label:"操作",width:"220px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return t.editPage(e.row.name)}}},[t._v("修改\n        ")]),t._v(" "),a("el-popconfirm",{attrs:{title:"确定要删除【 "+e.row.name+" 】吗？"},on:{confirm:function(a){return t.deleteApp(e.$index,e.row)}}},[a("el-button",{attrs:{slot:"reference",type:"text",icon:"el-icon-delete"},slot:"reference"},[t._v("删除\n          ")])],1),t._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-s-custom"},on:{click:function(a){return t.permPage(e.row)}}},[t._v("发布权限\n        ")]),t._v(" "),a("el-button",{staticStyle:{display:"none"},attrs:{type:"text",icon:"el-icon-position"},on:{click:function(a){return t.pipelinePage(e.row)}}},[t._v("发布流程\n        ")])]}}])})],1),t._v(" "),a("el-dialog",{attrs:{title:t.dialogEditTitle,visible:t.dialogEditVisible,width:"900px","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogEditVisible=e}}},[a("el-form",{ref:"dialogEditForm",attrs:{model:t.dialogEditForm,"label-width":"120px",rules:t.dialogEditFormRules}},[a("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}],attrs:{label:"ID"}},[a("el-input",{attrs:{disabled:!0},model:{value:t.dialogEditForm.id,callback:function(e){t.$set(t.dialogEditForm,"id",e)},expression:"dialogEditForm.id"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"应用名",prop:"name"}},[a("el-input",{attrs:{autocomplete:"off",disabled:t.dialogEditForm.id>0},model:{value:t.dialogEditForm.name,callback:function(e){t.$set(t.dialogEditForm,"name","string"===typeof e?e.trim():e)},expression:"dialogEditForm.name"}})],1),t._v(" "),a("el-form-item",{attrs:{label:"服务等级",prop:"level"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",disabled:!0},model:{value:t.dialogEditForm.level,callback:function(e){t.$set(t.dialogEditForm,"level",e)},expression:"dialogEditForm.level"}},[a("el-option",{attrs:{label:"L0-底层公共服务",value:"L0"}}),t._v(" "),a("el-option",{attrs:{label:"L1-核心业务服务",value:"L1"}}),t._v(" "),a("el-option",{attrs:{label:"L2-一般业务服务",value:"L2"}}),t._v(" "),a("el-option",{attrs:{label:"L3-非业务服务",value:"L3"}})],1),t._v(" "),a("div",{staticStyle:{"line-height":"normal",color:"#888","margin-top":"-5px","font-size":"12px"}},[t._v("\n          说明：自动同步自CRM【研发服务模块】对象的 服务等级 字段。\n          "),a("el-button",{staticStyle:{"font-size":"12px"},attrs:{type:"text"},on:{click:function(e){return t.ownerPage(t.dialogEditForm.name)}}},[t._v("去修改\n          ")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"所属部门"}},[a("el-input",{attrs:{autocomplete:"off",disabled:!0},model:{value:t.dialogEditForm.department,callback:function(e){t.$set(t.dialogEditForm,"department","string"===typeof e?e.trim():e)},expression:"dialogEditForm.department"}}),t._v(" "),a("div",{staticStyle:{"line-height":"normal",color:"#888","margin-top":"-5px","font-size":"12px"}},[t._v("\n          说明：自动同步自CRM【研发服务模块】对象的 Department 字段。\n          "),a("el-button",{staticStyle:{"font-size":"12px"},attrs:{type:"text"},on:{click:function(e){return t.ownerPage(t.dialogEditForm.name)}}},[t._v("去修改\n          ")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"第一负责人"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",disabled:!0,"filter-method":function(e){t.userPinYinMatch(e)}},model:{value:t.dialogEditForm.mainOwner,callback:function(e){t.$set(t.dialogEditForm,"mainOwner",e)},expression:"dialogEditForm.mainOwner"}},t._l(t.userOptions,(function(t){return a("el-option",{attrs:{label:t,value:t}})})),1),t._v(" "),a("div",{staticStyle:{"line-height":"normal",color:"#888","margin-top":"-5px","font-size":"12px"}},[t._v("\n          说明：主负责人，自动同步自CRM【研发服务模块】对象的 Owner 字段。\n          "),a("el-button",{staticStyle:{"font-size":"12px"},attrs:{type:"text"},on:{click:function(e){return t.ownerPage(t.dialogEditForm.name)}}},[t._v("去修改\n          ")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"负责人"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:"",filterable:"",disabled:!0,"filter-method":function(e){t.userPinYinMatch(e)}},model:{value:t.dialogEditForm.owners,callback:function(e){t.$set(t.dialogEditForm,"owners",e)},expression:"dialogEditForm.owners"}},t._l(t.userOptions,(function(t){return a("el-option",{attrs:{label:t,value:t}})})),1),t._v(" "),a("div",{staticStyle:{"line-height":"normal",color:"#888","margin-top":"-5px","font-size":"12px"}},[t._v("\n          说明：自动同步自CRM【研发服务模块】对象的 模块负责人(多选) 字段。\n          "),a("el-button",{staticStyle:{"font-size":"12px"},attrs:{type:"text"},on:{click:function(e){return t.ownerPage(t.dialogEditForm.name)}}},[t._v("去修改\n          ")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{attrs:{type:"textarea",rows:3,disabled:!0},model:{value:t.dialogEditForm.remark,callback:function(e){t.$set(t.dialogEditForm,"remark",e)},expression:"dialogEditForm.remark"}}),t._v(" "),a("div",{staticStyle:{"line-height":"normal",color:"#888","margin-top":"-5px","font-size":"12px"}},[t._v("\n          说明：自动同步自CRM【研发服务模块】对象的 用途说明 字段。\n          "),a("el-button",{staticStyle:{"font-size":"12px"},attrs:{type:"text"},on:{click:function(e){return t.ownerPage(t.dialogEditForm.name)}}},[t._v("去修改\n          ")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"发布时间窗口",prop:"timeWindow"}},[t._l(t.dialogEditForm.timeWindow,(function(e,i){return a("el-row",{staticStyle:{margin:"5px"}},[a("el-col",{attrs:{span:12}},[a("el-select",{staticStyle:{width:"90%"},attrs:{multiple:"",placeholder:"请选择"},model:{value:e.daysOfWeek,callback:function(a){t.$set(e,"daysOfWeek",a)},expression:"item.daysOfWeek"}},t._l(t.daysOfWeekOptions,(function(t){return a("el-option",{key:t.index,attrs:{label:t.label,value:t.value}})})),1)],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-time-picker",{staticStyle:{width:"80%"},attrs:{"is-range":"",format:"HH:mm","value-format":"HH:mm","range-separator":"至","start-placeholder":"开始时间","end-placeholder":"结束时间",placeholder:"选择时间范围"},model:{value:e.timeRange,callback:function(a){t.$set(e,"timeRange",a)},expression:"item.timeRange"}}),t._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(e){return t.delTimePeriod(i)}}},[t._v("删除\n            ")])],1)],1)})),t._v(" "),a("el-button",{attrs:{icon:"el-icon-plus",size:"medium"},on:{click:function(e){return t.addTimePeriod()}}},[t._v("添加\n        ")])],2)],1),t._v(" "),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(e){t.dialogEditVisible=!1}}},[t._v("取 消")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createOrUpdate()}}},[t._v("确 定")])],1)],1)],1)},l=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"margin-bottom":"10px","margin-top":"10px",border:"1px solid #eee"}},[a("table",{staticStyle:{color:"rgb(119, 119, 119)","font-size":"12px",padding:"10px"}},[a("tr",[a("th",{staticStyle:{width:"90px","text-align":"right"}},[t._v("服务等级:")]),t._v(" "),a("td",[t._v("参考文档：https://wiki.firstshare.cn/pages/viewpage.action?pageId=531923135 ")])]),t._v(" "),a("tr",[a("th",{staticStyle:{"text-align":"right"}},[t._v("第一负责人:")]),t._v(" "),a("td",[t._v("应用的主负责人，只能有一个。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等")])]),t._v(" "),a("tr",[a("th",{staticStyle:{"text-align":"right"}},[t._v("负责人:")]),t._v(" "),a("td",[t._v("应用的负责人，可以多个。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等")])]),t._v(" "),a("tr",[a("th",{staticStyle:{"text-align":"right"}},[t._v("发布权限:")]),t._v(" "),a("td",[t._v("授权角色内的成员可以对应用进行发布")])]),t._v(" "),a("tr",[a("th",{staticStyle:{"text-align":"right"}},[t._v("发布时间窗口:")]),t._v(" "),a("td",[t._v("应用可以发布的时间窗口。如果配置了，则只能在时间窗口范围内才能发布。如果没有配置，则任意时间都可以发布。")])])])])}],r=(a("6762"),a("2fdb"),a("4917"),a("7f7f"),a("2d63")),s=a("b562"),c=a("c24f"),d=a("d22a"),u=a("1e42"),m={name:"appList",components:{ExportButton:u["a"]},data:function(){var t=function(t,e,a){var i,n=Object(r["a"])(e);try{for(n.s();!(i=n.n()).done;){var o=i.value;if(!o.daysOfWeek||o.daysOfWeek.length<1)return void a(new Error("内容不能为空"));if(!o.timeRange||o.timeRange.length<1||!o.timeRange[0]||!o.timeRange[1])return void a(new Error("内容不能为空"))}}catch(l){n.e(l)}finally{n.f()}a()};return{pageLoading:!1,daysOfWeekOptions:[{value:1,label:"周一"},{value:2,label:"周二"},{value:3,label:"周三"},{value:4,label:"周四"},{value:5,label:"周五"},{value:6,label:"周六"},{value:7,label:"周日"}],searchForm:{keyword:"",department:"",level:"",page:1,limit:10},tableData:[],tableLoading:!1,dialogEditTitle:"",dialogEditVisible:!1,dialogEditForm:{id:0,name:"",level:"",department:"",orgs:[],mainOwner:"",owners:[],remark:"",timeWindow:[]},userAllOptions:[],userOptions:[],dialogEditFormRules:{name:[{required:!0,message:"请输入应用名",trigger:"blur"}],timeWindow:[{validator:t,trigger:"blur"}]}}},computed:{},watch:{},mounted:function(){this.searchForm.keyword=this.$route.query.app,this.loadTableData(),this.loadUserNames(),"true"===this.$route.query.showEditDialog&&this.$route.query.app&&this.editPage(this.$route.query.app)},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(s["o"])(this.searchForm).then((function(e){t.tableData=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},loadUserNames:function(){var t=this;Object(c["f"])().then((function(e){t.userOptions=e.data,t.userAllOptions=e.data})).catch((function(e){t.$message.error("加载用户数据出错：",e.message)}))},resetEditForm:function(){this.dialogEditForm.id=0,this.dialogEditForm.name="",this.dialogEditForm.org="",this.dialogEditForm.mainOwner="",this.dialogEditForm.owners=[],this.dialogEditForm.remark="",this.dialogEditForm.timeWindow=[]},PageChange:function(t){this.searchForm.page=t,this.loadTableData()},pageSizeChange:function(t){this.searchForm.limit=t,this.loadTableData()},createPage:function(){this.dialogEditTitle="新建",this.dialogEditVisible=!0,this.resetEditForm()},syncAppFromCRM:function(){var t=this;this.$confirm("确定要执行同步操作吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.pageLoading=!0,Object(s["p"])().then((function(e){t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error("操作失败：",e.message)})).finally((function(){t.pageLoading=!1}))}))},deleteApp:function(t,e){var a=this;Object(s["g"])(e.name).then((function(e){a.tableData.data.splice(t,1)})).catch((function(t){a.$message.error(t.message)}))},editPage:function(t){var e=this;Object(s["i"])(t).then((function(t){console.log(t.data),e.dialogEditForm=t.data,e.dialogEditTitle="修改",e.dialogEditVisible=!0})).catch((function(t){e.$message.error(t.message)}))},createOrUpdate:function(){var t=this;this.$refs["dialogEditForm"].validate((function(e){if(!e)return!1;var a=t.dialogEditForm.id>0?s["h"]:s["d"];a(t.dialogEditForm).then((function(e){t.dialogEditVisible=!1,t.$message.success("操作成功"),t.loadTableData()})).catch((function(e){t.$message.error(e.message)}))}))},permPage:function(t){this.$router.push({name:"app-permission",query:{app:t.name}})},pipelinePage:function(t){var e={app:t.name};this.$router.push({name:"cicd-app-deploy",query:e})},ownerPage:function(t){var e="https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_5FxRC__c";window.open(e)},addTimePeriod:function(){this.dialogEditForm.timeWindow.length<1?(this.dialogEditForm.timeWindow.push({daysOfWeek:[1,2,3,4,5,6,7],timeRange:["23:00","23:59"]}),this.dialogEditForm.timeWindow.push({daysOfWeek:[1,2,3,4,5,6,7],timeRange:["00:00","06:00"]})):this.dialogEditForm.timeWindow.push({daysOfWeek:[],timeRange:["22:00","23:59"]})},delTimePeriod:function(t){this.dialogEditForm.timeWindow.splice(t,1)},userPinYinMatch:function(t){t&&(this.userOptions=this.userAllOptions.reduce((function(e,a){return(d["a"].match(a,t)||a.includes(t))&&e.push(a),e}),[]))}}},p=m,f=a("2877"),g=Object(f["a"])(p,o,l,!1,null,null,null),b=g.exports,v=a("acaa"),h=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-alert",{staticStyle:{"font-size":"1.2em","background-color":"unset",border:"solid 1px #898282","margin-bottom":"10px","font-weight":"bold",color:"#333"},attrs:{title:"应用负责人管理页面",type:"info",closable:!1,description:"","show-icon":""}},[[a("div",{staticStyle:{"font-weight":"bold",color:"#333"}},[t._v("\n        发布系统、监控告警系统会使用这些负责人信息。\n      ")])]],2),t._v(" "),a("el-card",{staticClass:"box-card"},[a("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("应用负责人")]),a("span",{staticStyle:{"font-size":"14px"}},[t._v("(数据存储于配置中心的 cmdb-mark-v2.json 文件）")]),t._v(" "),a("el-button",{attrs:{type:"text",icon:"el-icon-tickets",size:"mini"},on:{click:t.toEditPage}},[t._v("进入配置中心")]),t._v(" "),a("export-button",{attrs:{icon:"el-icon-refresh","table-ref":this.$refs.table001}}),t._v(" "),a("el-button",{staticStyle:{color:"#888","font-size":"12px","margin-left":"50px"},attrs:{type:"text",icon:"el-icon-refresh"},on:{click:t.syncCMDBFromCRM}},[t._v("手动同步数据（从CRM到配置中心）")])],1),t._v(" "),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],ref:"table001",staticStyle:{width:"100%"},attrs:{data:t.tableData}},[a("el-table-column",{attrs:{prop:"service",label:"应用名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"level",label:"应用等级"}}),t._v(" "),a("el-table-column",{attrs:{prop:"mainOwner",label:"第一负责人"}}),t._v(" "),a("el-table-column",{attrs:{prop:"owner",label:"负责人"}}),t._v(" "),a("el-table-column",{attrs:{prop:"departments",label:"部门"}}),t._v(" "),a("el-table-column",{attrs:{prop:"category",label:"类别"}}),t._v(" "),a("el-table-column",{attrs:{prop:"crmId",label:"CRM ID"}}),t._v(" "),a("el-table-column",{attrs:{prop:"info",label:"描述"}})],1)],1)],1)},_=[],y=a("c356"),x={name:"cms-cmdb",components:{ExportButton:u["a"]},data:function(){return{loading:!1,tableData:[]}},created:function(){this.loadTable()},methods:{loadTable:function(){var t=this;this.loading=!0,Object(y["b"])().then((function(e){t.tableData=e.data.filter((function(t){return!(t.service&&t.service.includes("www.fxiaoke.com"))}))})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))},toEditPage:function(){window.open("https://console.foneshare.cn/cms/#/configuration/profile/detail/452","_blank")},syncCMDBFromCRM:function(){var t=this;this.$confirm("确定要执行同步操作吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.loading=!0,Object(y["c"])().then((function(e){t.$message.success("操作成功"),t.loadTable()})).catch((function(e){console.log(e),t.$message.error("操作失败：",e.message)})).finally((function(){t.loading=!1}))}))}}},w=x,k=(a("6332"),Object(f["a"])(w,h,_,!1,null,"939cfc88",null)),E=k.exports,F={components:{CmsCmdb:E,ArtifactList:v["default"],AppList:b},mounted:function(){},computed:{},data:function(){return{activeTab:"app"}},methods:{}},O=F,S=Object(f["a"])(O,i,n,!1,null,null,null);e["default"]=S.exports}}]);