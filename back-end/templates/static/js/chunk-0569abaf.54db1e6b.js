(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0569abaf"],{"30aa":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container",attrs:{"element-loading-text":"数据加载中"}},[a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:t.gitTagBatchDeletePage}},[t._v("批量删除分支或Tag")])],1),t._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:12}},[a("el-divider",{attrs:{"content-position":"left"}},[a("b",[t._v("创建Tag")]),t._v(" "),a("small",{staticStyle:{"padding-left":"10px"}},[t._v("应用名："+t._s(this.$route.query.app))])]),t._v(" "),a("el-form",{attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"Git地址"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:t.gitUrlChange},model:{value:t.form.gitUrl,callback:function(e){t.$set(t.form,"gitUrl",e)},expression:"form.gitUrl"}},t._l(this.forms,(function(t,e){return a("el-option",{key:t.gitUrl,attrs:{label:t.gitUrl,value:t.gitUrl}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"所属分支"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:""},model:{value:t.form.branch,callback:function(e){t.$set(t.form,"branch",e)},expression:"form.branch"}},t._l(t.form.branches,(function(t){return a("el-option",{key:t.name,attrs:{label:t.name,value:t.name}})})),1)],1),t._v(" "),a("el-form-item",{attrs:{label:"Tag名称"}},[a("el-input",{attrs:{placeholder:"请输入内容"},model:{value:t.form.nextTag,callback:function(e){t.$set(t.form,"nextTag",e)},expression:"form.nextTag"}},[a("template",{slot:"append"},[t._v(t._s(this.tagSuffix))])],2),t._v(" "),a("div",{staticStyle:{"text-align":"right"}},[a("el-checkbox",{model:{value:t.tagContainsBranchName,callback:function(e){t.tagContainsBranchName=e},expression:"tagContainsBranchName"}},[t._v("Tag后缀包含分支名")])],1)],1),t._v(" "),a("el-form-item",{attrs:{label:"描述"}},[a("el-input",{model:{value:t.form.msg,callback:function(e){t.$set(t.form,"msg",e)},expression:"form.msg"}})],1),t._v(" "),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-back"},on:{click:function(e){return t.buildImagePage()}}},[t._v("返回")]),t._v(" "),a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.createTag()}}},[t._v("立即创建")])],1)],1)],1),t._v(" "),a("el-col",{attrs:{span:12}},[a("el-divider",{attrs:{"content-position":"left"}},[a("b",[t._v("Tag列表")]),t._v(" "),a("small",{staticStyle:{"padding-left":"10px"}},[t._v("(只展现最近的 200 个，默认按照对应的Commit时间倒序)")])]),t._v(" "),a("el-table",{key:"name",staticStyle:{width:"100%","max-height":"700px",overflow:"auto"},attrs:{data:t.form.tags}},[a("el-table-column",{attrs:{type:"index"}}),t._v(" "),a("el-table-column",{attrs:{prop:"name",sortable:"",label:"Tag名称"}}),t._v(" "),a("el-table-column",{attrs:{prop:"commitCreatedAt",label:"对应Commit时间",sortable:""}}),t._v(" "),a("el-table-column",{attrs:{prop:"message",label:"描述"}})],1)],1)],1)],1)},r=[],s=a("b562"),c=a("c1df"),i=a.n(c),o=a("b144"),l={data:function(){return{loading:!1,forms:[],form:{},tagContainsBranchName:!0,multipleSelectionTags:[]}},computed:{tagSuffix:function(){var t="";return this.tagContainsBranchName&&this.form.branch&&(t="-"+this.form.branch),t=t+"-"+this.dateFormat(),t}},mounted:function(){this.loadData()},methods:{loadData:function(){var t=this;this.loading=!0,Object(s["n"])(this.$route.query.app).then((function(e){t.forms=e.data;var a=t.forms[0].gitUrl;t.$route.query.gitUrl&&(a=t.$route.query.gitUrl),t.form=t.getForm(t.form.gitUrl?t.form.gitUrl:a)})).catch((function(e){t.$message.warning(e.message)})).finally((function(){t.loading=!1}))},dateFormat:function(){return i()().format("YYYYMMDD")},pipelinePage:function(){this.$router.push({name:"cicd-app-deploy",query:{app:this.$route.query.app}})},buildImagePage:function(t){this.$router.push({name:"cicd-image-build",query:{app:this.$route.query.app}})},createTag:function(){var t=this,e={};e.gitUrl=this.form.gitUrl,e.branch=this.form.branch,e.tag=this.form.nextTag+this.tagSuffix,e.msg=this.form.msg,Object(s["f"])(e).then((function(e){t.$message.success("创建成功"),t.buildImagePage()})).catch((function(e){t.$message.warning("创建tag失败！ "+e.message)}))},getForm:function(t){if(!t||!this.forms||!this.form.length<1)return{};var e=this.forms.filter((function(e){return e.gitUrl===t}));return e.length>0?Object(o["a"])(e[0]):{}},gitUrlChange:function(t){this.form=this.getForm(t)},gitTagBatchDeletePage:function(){var t=this.$router.resolve({name:"git-tag-batch-delete",query:{app:this.$route.query.app,git_url:this.form.gitUrl}});window.open(t.href,"_blank")}}},u=l,f=a("2877"),d=Object(f["a"])(u,n,r,!1,null,"129927fe",null);e["default"]=d.exports},4678:function(t,e,a){var n={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"73332","./en-il.js":"73332","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e9","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e9","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function r(t){var e=s(t);return a(e)}function s(t){var e=n[t];if(!(e+1)){var a=new Error("Cannot find module '"+t+"'");throw a.code="MODULE_NOT_FOUND",a}return e}r.keys=function(){return Object.keys(n)},r.resolve=s,t.exports=r,r.id="4678"},b144:function(t,e,a){"use strict";function n(t){return JSON.parse(JSON.stringify(t))}function r(t){if(!t||!(t instanceof Date))return"";var e=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds();return e}function s(t){return"isCoreApp"===t?"核心服务":"onlyDeployTag"===t?"只允许部署Tag":"addSysctlKeepalive"===t?"调整内核参数":"skyWalkingAgent"===t?"性能跟踪":"appLogToKafka"===t?"接入ClickHouse日志":"buildUseRuntimeJDK"===t?"镜像JDK版本编译代码":"jvmGcLog"===t?"GC日志":t}a.d(e,"a",(function(){return n})),a.d(e,"c",(function(){return r})),a.d(e,"b",(function(){return s}))},b562:function(t,e,a){"use strict";a.d(e,"o",(function(){return r})),a.d(e,"b",(function(){return s})),a.d(e,"a",(function(){return c})),a.d(e,"k",(function(){return i})),a.d(e,"i",(function(){return o})),a.d(e,"d",(function(){return l})),a.d(e,"h",(function(){return u})),a.d(e,"g",(function(){return f})),a.d(e,"l",(function(){return d})),a.d(e,"n",(function(){return b})),a.d(e,"f",(function(){return m})),a.d(e,"e",(function(){return j})),a.d(e,"c",(function(){return g})),a.d(e,"j",(function(){return p})),a.d(e,"q",(function(){return h})),a.d(e,"m",(function(){return v})),a.d(e,"p",(function(){return y}));var n=a("b775");function r(t){return Object(n["a"])({url:"/v1/app/search",method:"get",params:t})}function s(){return Object(n["a"])({url:"/v1/app/apps-with-env",method:"get"})}function c(){return Object(n["a"])({url:"/v1/app/all",method:"get"})}function i(){return Object(n["a"])({url:"/v1/app/names",method:"get"})}function o(t){return Object(n["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function l(t){return Object(n["a"])({url:"/v1/app",method:"post",data:t})}function u(t){return Object(n["a"])({url:"/v1/app",method:"put",data:t})}function f(t){return Object(n["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function d(t,e,a){return Object(n["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:a}})}function b(t){return Object(n["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function m(t){return Object(n["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function j(t){return Object(n["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function g(t){return Object(n["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function p(t,e){return Object(n["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function h(t,e){return Object(n["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function v(t,e){return Object(n["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function y(){return Object(n["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}}}]);