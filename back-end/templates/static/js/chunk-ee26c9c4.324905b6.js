(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-ee26c9c4"],{"11e9":function(t,e,n){var a=n("52a7"),r=n("4630"),o=n("6821"),i=n("6a99"),s=n("69a8"),l=n("c69a"),c=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?c:function(t,e){if(t=o(t),e=i(e,!0),l)try{return c(t,e)}catch(n){}if(s(t,e))return r(!a.f.call(t,e),t[e])}},"1e42":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{display:"inline"}},[n("el-button",{staticStyle:{"margin-left":"10px","margin-right":"10px","font-size":"12px"},attrs:{type:this.buttonType,size:this.buttonSize,icon:"el-icon-download"},on:{click:t.exportExcel}},[t._v("导出")])],1)},r=[],o=(n("a481"),n("25ca")),i=n("21a6"),s=n.n(i),l={name:"export-button",components:{},props:{tableRef:{type:Object},buttonType:{type:String,default:"text"},fileName:{type:String,default:"export"},buttonSize:{type:String,default:""}},data:function(){return{}},computed:{},mounted:function(){},methods:{exportExcel:function(){if(this.tableRef){var t=this.tableRef.$el,e=o["a"].table_to_book(t,{raw:!0}),n=o["b"](e,{bookType:"xlsx",bookSST:!0,type:"array"});try{var a=this.fileName+"-"+(new Date).toISOString().replace(/T/,"-").replace(/\..+/,"").replace(/[_\-:]/g,"")+".xlsx";s.a.saveAs(new Blob([n],{type:"application/octet-stream"}),a)}catch(r){this.$message.error("导出失败, err: "+r.message),console.error(r)}return n}this.$message.error("请通过table-ref属性指定要导出的表格ref名")}}},c=l,u=n("2877"),p=Object(u["a"])(c,a,r,!1,null,null,null);e["a"]=p.exports},"28a5":function(t,e,n){"use strict";var a=n("aae3"),r=n("cb7c"),o=n("ebd6"),i=n("0390"),s=n("9def"),l=n("5f1b"),c=n("520a"),u=n("79e5"),p=Math.min,d=[].push,f="split",m="length",b="lastIndex",v=4294967295,h=!u((function(){RegExp(v,"y")}));n("214f")("split",2,(function(t,e,n,u){var g;return g="c"=="abbc"[f](/(b)*/)[1]||4!="test"[f](/(?:)/,-1)[m]||2!="ab"[f](/(?:ab)*/)[m]||4!="."[f](/(.?)(.?)/)[m]||"."[f](/()()/)[m]>1||""[f](/.?/)[m]?function(t,e){var r=String(this);if(void 0===t&&0===e)return[];if(!a(t))return n.call(r,t,e);var o,i,s,l=[],u=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),p=0,f=void 0===e?v:e>>>0,h=new RegExp(t.source,u+"g");while(o=c.call(h,r)){if(i=h[b],i>p&&(l.push(r.slice(p,o.index)),o[m]>1&&o.index<r[m]&&d.apply(l,o.slice(1)),s=o[0][m],p=i,l[m]>=f))break;h[b]===o.index&&h[b]++}return p===r[m]?!s&&h.test("")||l.push(""):l.push(r.slice(p)),l[m]>f?l.slice(0,f):l}:"0"[f](void 0,0)[m]?function(t,e){return void 0===t&&0===e?[]:n.call(this,t,e)}:n,[function(n,a){var r=t(this),o=void 0==n?void 0:n[e];return void 0!==o?o.call(n,r,a):g.call(String(r),n,a)},function(t,e){var a=u(g,t,this,e,g!==n);if(a.done)return a.value;var c=r(t),d=String(this),f=o(c,RegExp),m=c.unicode,b=(c.ignoreCase?"i":"")+(c.multiline?"m":"")+(c.unicode?"u":"")+(h?"y":"g"),_=new f(h?c:"^(?:"+c.source+")",b),y=void 0===e?v:e>>>0;if(0===y)return[];if(0===d.length)return null===l(_,d)?[d]:[];var w=0,x=0,O=[];while(x<d.length){_.lastIndex=h?x:0;var k,j=l(_,h?d:d.slice(x));if(null===j||(k=p(s(_.lastIndex+(h?0:x)),d.length))===w)x=i(d,x,m);else{if(O.push(d.slice(w,x)),O.length===y)return O;for(var S=1;S<=j.length-1;S++)if(O.push(j[S]),O.length===y)return O;x=w=k}}return O.push(d.slice(w)),O}]}))},"4f7f":function(t,e,n){"use strict";var a=n("c26b"),r=n("b39a"),o="Set";t.exports=n("e0b8")(o,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return a.def(r(this,o),t=0===t?0:t,t)}},a)},5147:function(t,e,n){var a=n("2b4c")("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(n){try{return e[a]=!1,!"/./"[t](e)}catch(r){}}return!0}},"51a9":function(t,e,n){"use strict";n.d(e,"c",(function(){return r})),n.d(e,"e",(function(){return o})),n.d(e,"d",(function(){return i})),n.d(e,"l",(function(){return s})),n.d(e,"m",(function(){return l})),n.d(e,"a",(function(){return c})),n.d(e,"f",(function(){return u})),n.d(e,"i",(function(){return p})),n.d(e,"j",(function(){return d})),n.d(e,"k",(function(){return f})),n.d(e,"n",(function(){return m})),n.d(e,"g",(function(){return b})),n.d(e,"b",(function(){return v})),n.d(e,"h",(function(){return h})),n.d(e,"o",(function(){return g}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/pipeline/app/"+t,method:"get"})}function o(t){return Object(a["a"])({url:"/v1/pipeline",method:"get",params:{id:t}})}function i(t,e,n){return Object(a["a"])({url:"/v1/pipeline/find-by-env",method:"get",params:{cluster:t,namespace:e,app:n}})}function s(t){return Object(a["a"])({url:"/v1/pipeline/search",method:"get",params:t})}function l(t){return Object(a["a"])({url:"/v1/pipeline/search-by-properties",method:"post",data:t})}function c(){return Object(a["a"])({url:"/v1/pipeline/all",method:"get"})}function u(t){return Object(a["a"])({url:"/v1/pipeline/status",method:"get",params:{status:t}})}function p(t){return Object(a["a"])({url:"/v1/pipeline/init",method:"post",params:{app:t}})}function d(t){return Object(a["a"])({url:"/v1/pipeline",method:"post",data:t})}function f(t){return Object(a["a"])({url:"/v1/pipeline/offline",method:"delete",params:{id:t}})}function m(t){return Object(a["a"])({url:"/v1/pipeline/sync",method:"post",data:t})}function b(t,e,n,r){return Object(a["a"])({url:"/v1/pipeline/clone/by-namespace",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:r}})}function v(t){return Object(a["a"])({url:"/v1/pipeline/clone/by-namespace",method:"post",data:t})}function h(t,e,n,r){return Object(a["a"])({url:"/v1/pipeline/publish-dedicated-cloud",method:"get",params:{sourceCluster:t,sourceNamespace:e,targetCluster:n,targetNamespace:r}})}function g(t){return Object(a["a"])({url:"/v1/pipeline/status",method:"post",data:t})}},"5dbc":function(t,e,n){var a=n("d3f4"),r=n("8b97").set;t.exports=function(t,e,n){var o,i=e.constructor;return i!==n&&"function"==typeof i&&(o=i.prototype)!==n.prototype&&a(o)&&r&&r(t,o),t}},"5df3":function(t,e,n){"use strict";var a=n("02f4")(!0);n("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=a(e,n),this._i+=t.length,{value:t,done:!1})}))},"62ef":function(t,e,n){},6797:function(t,e,n){"use strict";n.d(e,"d",(function(){return r})),n.d(e,"c",(function(){return o})),n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return s}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/sys/log",method:"get",params:{tailLines:t}})}function o(){return Object(a["a"])({url:"/v1/sys/oncall-config",method:"get"})}function i(){return Object(a["a"])({url:"/v1/sys/lb-pools",method:"get"})}function s(){return Object(a["a"])({url:"/v1/sys/setting/cache",method:"delete"})}},"67ab":function(t,e,n){var a=n("ca5a")("meta"),r=n("d3f4"),o=n("69a8"),i=n("86cc").f,s=0,l=Object.isExtensible||function(){return!0},c=!n("79e5")((function(){return l(Object.preventExtensions({}))})),u=function(t){i(t,a,{value:{i:"O"+ ++s,w:{}}})},p=function(t,e){if(!r(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!o(t,a)){if(!l(t))return"F";if(!e)return"E";u(t)}return t[a].i},d=function(t,e){if(!o(t,a)){if(!l(t))return!0;if(!e)return!1;u(t)}return t[a].w},f=function(t){return c&&m.NEED&&l(t)&&!o(t,a)&&u(t),t},m=t.exports={KEY:a,NEED:!1,fastKey:p,getWeak:d,onFreeze:f}},"768b":function(t,e,n){"use strict";var a=n("a745"),r=n.n(a);function o(t){if(r()(t))return t}var i=n("67bb"),s=n.n(i),l=n("5d58"),c=n.n(l);function u(t,e){var n=null==t?null:"undefined"!==typeof s.a&&t[c.a]||t["@@iterator"];if(null!=n){var a,r,o=[],i=!0,l=!1;try{for(n=n.call(t);!(i=(a=n.next()).done);i=!0)if(o.push(a.value),e&&o.length===e)break}catch(u){l=!0,r=u}finally{try{i||null==n["return"]||n["return"]()}finally{if(l)throw r}}return o}}var p=n("e630");function d(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function f(t,e){return o(t)||u(t,e)||Object(p["a"])(t,e)||d()}n.d(e,"a",(function(){return f}))},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return r})),n.d(e,"a",(function(){return o})),n.d(e,"h",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"b",(function(){return l})),n.d(e,"i",(function(){return c})),n.d(e,"d",(function(){return u})),n.d(e,"f",(function(){return p})),n.d(e,"e",(function(){return d}));var a=n("b775");function r(t,e){return Object(a["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function o(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function i(t){return Object(a["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function s(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function l(t){return Object(a["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function c(t){return Object(a["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function u(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function p(t){return Object(a["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,n,r,o){return Object(a["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:r,deployTag:o||""}})}},"8b97":function(t,e,n){var a=n("d3f4"),r=n("cb7c"),o=function(t,e){if(r(t),!a(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,a){try{a=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),a(t,[]),e=!(t instanceof Array)}catch(r){e=!0}return function(t,n){return o(t,n),e?t.__proto__=n:a(t,n),t}}({},!1):void 0),check:o}},aae3:function(t,e,n){var a=n("d3f4"),r=n("2d95"),o=n("2b4c")("match");t.exports=function(t){var e;return a(t)&&(void 0!==(e=t[o])?!!e:"RegExp"==r(t))}},ad8b:function(t,e,n){"use strict";n("62ef")},aef6:function(t,e,n){"use strict";var a=n("5ca1"),r=n("9def"),o=n("d2c8"),i="endsWith",s=""[i];a(a.P+a.F*n("5147")(i),"String",{endsWith:function(t){var e=o(this,t,i),n=arguments.length>1?arguments[1]:void 0,a=r(e.length),l=void 0===n?a:Math.min(r(n),a),c=String(t);return s?s.call(e,c,l):e.slice(l-c.length,l)===c}})},b39a:function(t,e,n){var a=n("d3f4");t.exports=function(t,e){if(!a(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},b562:function(t,e,n){"use strict";n.d(e,"o",(function(){return r})),n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return i})),n.d(e,"k",(function(){return s})),n.d(e,"i",(function(){return l})),n.d(e,"d",(function(){return c})),n.d(e,"h",(function(){return u})),n.d(e,"g",(function(){return p})),n.d(e,"l",(function(){return d})),n.d(e,"n",(function(){return f})),n.d(e,"f",(function(){return m})),n.d(e,"e",(function(){return b})),n.d(e,"c",(function(){return v})),n.d(e,"j",(function(){return h})),n.d(e,"q",(function(){return g})),n.d(e,"m",(function(){return _})),n.d(e,"p",(function(){return y}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/app/search",method:"get",params:t})}function o(){return Object(a["a"])({url:"/v1/app/apps-with-env",method:"get"})}function i(){return Object(a["a"])({url:"/v1/app/all",method:"get"})}function s(){return Object(a["a"])({url:"/v1/app/names",method:"get"})}function l(t){return Object(a["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function c(t){return Object(a["a"])({url:"/v1/app",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/v1/app",method:"put",data:t})}function p(t){return Object(a["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function d(t,e,n){return Object(a["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:n}})}function f(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function m(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function h(t,e){return Object(a["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function g(t,e){return Object(a["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function _(t,e){return Object(a["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function y(){return Object(a["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}},c1ab:function(t,e,n){"use strict";n.d(e,"h",(function(){return r})),n.d(e,"j",(function(){return o})),n.d(e,"z",(function(){return i})),n.d(e,"A",(function(){return s})),n.d(e,"c",(function(){return l})),n.d(e,"f",(function(){return c})),n.d(e,"E",(function(){return u})),n.d(e,"G",(function(){return p})),n.d(e,"y",(function(){return d})),n.d(e,"a",(function(){return f})),n.d(e,"e",(function(){return m})),n.d(e,"D",(function(){return b})),n.d(e,"F",(function(){return v})),n.d(e,"x",(function(){return h})),n.d(e,"H",(function(){return g})),n.d(e,"k",(function(){return _})),n.d(e,"d",(function(){return y})),n.d(e,"B",(function(){return w})),n.d(e,"i",(function(){return x})),n.d(e,"g",(function(){return O})),n.d(e,"s",(function(){return k})),n.d(e,"v",(function(){return j})),n.d(e,"w",(function(){return S})),n.d(e,"o",(function(){return C})),n.d(e,"p",(function(){return A})),n.d(e,"t",(function(){return N})),n.d(e,"u",(function(){return $})),n.d(e,"b",(function(){return D})),n.d(e,"q",(function(){return L})),n.d(e,"r",(function(){return R})),n.d(e,"n",(function(){return T})),n.d(e,"C",(function(){return F})),n.d(e,"m",(function(){return E})),n.d(e,"l",(function(){return U}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function o(t){return Object(a["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function i(t){return Object(a["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function s(t){return Object(a["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function l(){return Object(a["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function c(t){return Object(a["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function f(){return Object(a["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function m(t,e,n,r,o,i,s,l){return Object(a["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(o,"&fixVersion=").concat(e,"&suffixVersion=").concat(n,"&message=").concat(r,"&dependencyCheck=").concat(i,"&parentPom=").concat(s),method:"post",data:l})}function b(t){return Object(a["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function h(t){return Object(a["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function _(t,e,n,r){return Object(a["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(n,"&overrideNamespace=").concat(r),method:"post"})}function y(t,e,n,r,o){return Object(a["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(n,"&remark=").concat(r,"&dryRun=").concat(o),method:"post"})}function w(){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function x(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function O(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function k(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function j(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function S(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function C(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function A(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function N(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function $(t,e){return Object(a["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function D(t){return Object(a["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function L(t){return Object(a["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function R(t){return Object(a["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function T(t,e,n){return Object(a["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(n),method:"get"})}function F(t){return Object(a["a"])({url:"/v1/tool/search-cms-configs?keyword=".concat(t),method:"get"})}function E(t,e,n){return Object(a["a"])({url:"/v1/tool/load-app-by-lb-addr?cluster=".concat(t,"&namespace=").concat(e,"&lbAddr=").concat(n),method:"get"})}function U(){return Object(a["a"])({url:"/v1/tool/load-apibus-addr",method:"get"})}},c26b:function(t,e,n){"use strict";var a=n("86cc").f,r=n("2aeb"),o=n("dcbc"),i=n("9b43"),s=n("f605"),l=n("4a59"),c=n("01f9"),u=n("d53b"),p=n("7a56"),d=n("9e1e"),f=n("67ab").fastKey,m=n("b39a"),b=d?"_s":"size",v=function(t,e){var n,a=f(e);if("F"!==a)return t._i[a];for(n=t._f;n;n=n.n)if(n.k==e)return n};t.exports={getConstructor:function(t,e,n,c){var u=t((function(t,a){s(t,u,e,"_i"),t._t=e,t._i=r(null),t._f=void 0,t._l=void 0,t[b]=0,void 0!=a&&l(a,n,t[c],t)}));return o(u.prototype,{clear:function(){for(var t=m(this,e),n=t._i,a=t._f;a;a=a.n)a.r=!0,a.p&&(a.p=a.p.n=void 0),delete n[a.i];t._f=t._l=void 0,t[b]=0},delete:function(t){var n=m(this,e),a=v(n,t);if(a){var r=a.n,o=a.p;delete n._i[a.i],a.r=!0,o&&(o.n=r),r&&(r.p=o),n._f==a&&(n._f=r),n._l==a&&(n._l=o),n[b]--}return!!a},forEach:function(t){m(this,e);var n,a=i(t,arguments.length>1?arguments[1]:void 0,3);while(n=n?n.n:this._f){a(n.v,n.k,this);while(n&&n.r)n=n.p}},has:function(t){return!!v(m(this,e),t)}}),d&&a(u.prototype,"size",{get:function(){return m(this,e)[b]}}),u},def:function(t,e,n){var a,r,o=v(t,e);return o?o.v=n:(t._l=o={i:r=f(e,!0),k:e,v:n,p:a=t._l,n:void 0,r:!1},t._f||(t._f=o),a&&(a.n=o),t[b]++,"F"!==r&&(t._i[r]=o)),t},getEntry:v,setStrong:function(t,e,n){c(t,e,(function(t,n){this._t=m(t,e),this._k=n,this._l=void 0}),(function(){var t=this,e=t._k,n=t._l;while(n&&n.r)n=n.p;return t._t&&(t._l=n=n?n.n:t._t._f)?u(0,"keys"==e?n.k:"values"==e?n.v:[n.k,n.v]):(t._t=void 0,u(1))}),n?"entries":"values",!n,!0),p(e)}}},d0cf:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-tabs",{on:{"tab-click":t.handleClick},model:{value:t.activeTab,callback:function(e){t.activeTab=e},expression:"activeTab"}},[n("el-tab-pane",{attrs:{label:"集群lb拆分",name:"cluster-lb-split",lazy:!0}},[n("cluster-migrate-lb-split")],1),t._v(" "),n("el-tab-pane",{attrs:{label:"集群lb拆分(ApiBus)",name:"cluster-lb-split-apibus",lazy:!0}},[n("cluster-migrate-lb-split-apibus")],1),t._v(" "),n("el-tab-pane",{attrs:{label:"老集群（k8s1)",name:"old-k8s",lazy:!0}},[n("cluster-migrate-old")],1),t._v(" "),n("el-tab-pane",{attrs:{label:"新集群（k8s0)",name:"new-k8s",lazy:!0}},[n("cluster-migrate-new")],1),t._v(" "),n("el-tab-pane",{attrs:{label:"应用迁移处理",name:"migrate-operation",lazy:!0}},[n("cluster-migrate-operation")],1),t._v(" "),n("el-tab-pane",{attrs:{label:"发布流程处理",name:"pipeline-batch-operation",lazy:!0}},[n("cluster-migrate-pipeline-batch-operation")],1)],1)],1)},r=[],o=n("db72"),i=(n("7f7f"),function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[n("div",[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.form}},[n("el-form-item",{attrs:{label:"环境"}},[n("el-select",{staticStyle:{width:"260px"},attrs:{"value-key":"id",filterable:""},model:{value:t.form.env,callback:function(e){t.$set(t.form,"env",e)},expression:"form.env"}},t._l(t.envOptions,(function(t){return n("el-option",{key:t.id,attrs:{label:t.id,value:t}})})),1)],1),t._v(" "),n("el-form-item",{attrs:{label:"LB地址"}},[n("el-select",{staticStyle:{width:"420px"},attrs:{filterable:""},model:{value:t.form.lbAddr,callback:function(e){t.$set(t.form,"lbAddr",e)},expression:"form.lbAddr"}},t._l(t.lbOptions,(function(t){return n("el-option",{key:t.addr,attrs:{label:t.addr+" / "+t.name+" / "+t.remark,value:t.addr}})})),1)],1),t._v(" "),n("el-form-item",[n("el-button",{attrs:{type:"primary"},on:{click:t.loadData}},[t._v("查询")]),t._v(" "),n("export-button",{attrs:{"table-ref":this.$refs.table001,"file-name":"k8s-lb-split"}})],1)],1)],1),t._v(" "),n("div",[n("el-table",{ref:"table001",staticStyle:{width:"100%"},attrs:{data:t.data,size:"mini","highlight-selection-row":!0}},[n("el-table-column",{attrs:{type:"selection",width:"50"}}),t._v(" "),n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{prop:"app",label:"应用名称",sortable:""}}),t._v(" "),n("el-table-column",{attrs:{prop:"appLevel",label:"应用等级",width:"80"}}),t._v(" "),n("el-table-column",{attrs:{prop:"appMainOwner",label:"应用负责人",width:"120"}}),t._v(" "),n("el-table-column",{attrs:{label:"环境"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.cluster)+" / "+t._s(e.row.namespace)+"\n        ")]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"portName",label:"端口名"}}),t._v(" "),n("el-table-column",{attrs:{prop:"remark",label:"Remark"}}),t._v(" "),n("el-table-column",{attrs:{prop:"beforeAddr",label:"Before地址 (被引用配置文件数)","min-width":"220"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.beforeAddr))]),t._v(" "),n("span",[n("clipboard-icon",{attrs:{text:e.row.beforeAddr}})],1),t._v(" "),n("span",{staticStyle:{"font-weight":"bold",color:"#e36a08"}},[t._v("("+t._s(e.row.beforeAddrCmsRefCounter)+")")])]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"afterAddr",label:"After地址 (被引用配置文件数)","min-width":"220"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.afterAddr))]),t._v(" "),n("span",[n("clipboard-icon",{attrs:{text:e.row.afterAddr}})],1),t._v(" "),n("span",{staticStyle:{"font-weight":"bold",color:"#e36a08"}},[t._v("("+t._s(e.row.afterAddrCmsRefCounter)+")")])]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"cmsRefCounter",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:function(n){return t.batchEditCMS("",e.row.beforeAddr,e.row.afterAddr)}}},[t._v("去修改")]),t._v(" "),n("el-button",{staticStyle:{color:"#999"},attrs:{type:"text"},on:{click:function(n){return t.batchEditCMS("",e.row.afterAddr,e.row.beforeAddr,"确定要打开回滚页面吗？")}}},[t._v("回滚")])]}}])})],1)],1)])}),s=[],l=n("2d63"),c=n("c1ab"),u=n("6797"),p=n("1e42"),d=n("da37"),f={components:{ClipboardIcon:d["a"],ExportButton:p["a"]},data:function(){return{form:{env:{cluster:null,namespace:null},lbAddr:""},data:[],lbPools:[],loading:!1}},mounted:function(){this.loadLBPools()},computed:{lbOptions:function(){var t,e=[],n=Object(l["a"])(this.lbPools);try{for(n.s();!(t=n.n()).done;){var a=t.value;if(a.cluster===this.form.env.cluster){var r,o=Object(l["a"])(a.lbAllocList);try{for(o.s();!(r=o.n()).done;){var i=r.value;e.push({name:i.name,addr:i.addr,remark:i.remark})}}catch(s){o.e(s)}finally{o.f()}}}}catch(s){n.e(s)}finally{n.f()}return e.length>0&&e.unshift({name:"_all_",addr:"_all_",remark:"所有LB地址"}),e},envOptions:function(){var t,e=[],n=Object(l["a"])(this.$settings.clusters);try{for(n.s();!(t=n.n()).done;){var a=t.value;e.push({cluster:a.name,namespace:"_all_",id:a.name+"/_all_"});var r,o=Object(l["a"])(a.namespaces);try{for(o.s();!(r=o.n()).done;){var i=r.value,s={};s.cluster=a.name,s.namespace=i,s.id=a.name+"/"+i,e.push(s)}}catch(c){o.e(c)}finally{o.f()}}}catch(c){n.e(c)}finally{n.f()}return e}},methods:{loadData:function(){var t=this;this.form.env.cluster&&this.form.env.namespace&&this.form.lbAddr?(this.loading=!0,Object(c["m"])(this.form.env.cluster,this.form.env.namespace,this.form.lbAddr).then((function(e){var n,a=[],r=Object(l["a"])(e.data);try{for(r.s();!(n=r.n()).done;){var o=n.value;t.fnSearchCmsConfig(o),a.push(o)}}catch(i){r.e(i)}finally{r.f()}t.data=a,t.$message.success("数据加载成功")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))):this.$message.error("请选择查询条件")},loadLBPools:function(){var t=this;Object(u["b"])().then((function(e){t.lbPools=e.data})).catch((function(e){t.$message.error("lb pools load fail, err:"+e.message)}))},batchEditCMS:function(t,e,n,a){if(a)this.$confirm(a,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="/api/page/redirect?type=cmsBatchEdit&profile=".concat(t,"&oldContent=").concat(e,"&newContent=").concat(n,"&_t")+Date.now();window.open(a)}));else{var r="/api/page/redirect?type=cmsBatchEdit&profile=".concat(t,"&oldContent=").concat(e,"&newContent=").concat(n,"&_t")+Date.now();window.open(r)}},fnSearchCmsConfig:function(t){t.beforeAddrCmsRefCounter="--",Object(c["C"])(t.beforeAddr).then((function(e){t.beforeAddrCmsRefCounter=e.data.length})).catch((function(e){t.beforeAddrCmsRefCounter="error",console.log(e.message)})).finally((function(){})),t.afterAddrCmsRefCounter="--",Object(c["C"])(t.afterAddr).then((function(e){t.afterAddrCmsRefCounter=e.data.length})).catch((function(e){t.afterAddrCmsRefCounter="error",console.log(e.message)})).finally((function(){}))}}},m=f,b=n("2877"),v=Object(b["a"])(m,i,s,!1,null,"5b5ca77d",null),h=v.exports,g=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[n("div",[n("div",{staticStyle:{position:"absolute",right:"30px",top:"-10px","z-index":"999"}},[n("export-button",{attrs:{"table-ref":this.$refs.table001,"file-name":"lb-split-for-apibus"}})],1),t._v(" "),n("el-table",{ref:"table001",staticStyle:{width:"100%"},attrs:{data:t.data,size:"mini","highlight-selection-row":!0}},[n("el-table-column",{attrs:{type:"index",width:"30"}}),t._v(" "),n("el-table-column",{attrs:{type:"selection",width:"50"}}),t._v(" "),n("el-table-column",{attrs:{prop:"app",label:"应用名称",sortable:""}}),t._v(" "),n("el-table-column",{attrs:{label:"环境"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n          "+t._s(e.row.cluster)+" / "+t._s(e.row.namespace)+"\n        ")]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"runningPodNum",label:"运行副本",width:"80"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",{style:isNaN(Number(e.row.runningPodNum))||0===e.row.runningPodNum?"color:orangered;font-weight:bold;":""},[t._v("\n            "+t._s(e.row.runningPodNum)+"\n          ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"portName",label:"端口名"}}),t._v(" "),n("el-table-column",{attrs:{prop:"beforeAddr",label:"Before地址 (被引用配置文件数)","min-width":"220"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.beforeAddr))]),t._v(" "),n("span",[n("clipboard-icon",{attrs:{text:e.row.beforeAddr}})],1),t._v(" "),n("span",{staticStyle:{"font-weight":"bold",color:"#e36a08"}},[t._v("("+t._s(e.row.beforeAddrCmsRefCounter)+")")])]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"afterAddr",label:"After地址 (被引用配置文件数)","min-width":"220"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(e.row.afterAddr))]),t._v(" "),n("span",[n("clipboard-icon",{attrs:{text:e.row.afterAddr}})],1),t._v(" "),n("span",{staticStyle:{"font-weight":"bold",color:"#e36a08"}},[t._v("("+t._s(e.row.afterAddrCmsRefCounter)+")")])]}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"cmsRefCounter",label:"操作"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:function(n){return t.batchEditCMS("",e.row.beforeAddr,e.row.afterAddr)}}},[t._v("去修改")]),t._v(" "),n("el-button",{staticStyle:{color:"#999"},attrs:{type:"text"},on:{click:function(n){return t.batchEditCMS("",e.row.afterAddr,e.row.beforeAddr,"确定要打开回滚页面吗？")}}},[t._v("回滚")])]}}])})],1)],1)])},_=[],y=n("8504"),w={components:{ExportButton:p["a"],ClipboardIcon:d["a"]},data:function(){return{data:[],loading:!1}},mounted:function(){this.loadData()},computed:{},methods:{loadData:function(){var t=this;this.loading=!0,Object(c["l"])().then((function(e){var n,a=[],r=Object(l["a"])(e.data);try{for(r.s();!(n=r.n()).done;){var o=n.value;t.fnSearchCmsConfig(o),t.findDeployment(o),a.push(o)}}catch(i){r.e(i)}finally{r.f()}t.data=a,t.$message.success("数据加载成功")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))},batchEditCMS:function(t,e,n,a){if(a)this.$confirm(a,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var a="/api/page/redirect?type=cmsBatchEdit&profile=".concat(t,"&oldContent=").concat(e,"&newContent=").concat(n,"&_t")+Date.now();window.open(a)}));else{var r="/api/page/redirect?type=cmsBatchEdit&profile=".concat(t,"&oldContent=").concat(e,"&newContent=").concat(n,"&_t")+Date.now();window.open(r)}},fnSearchCmsConfig:function(t){t.beforeAddrCmsRefCounter="--",Object(c["C"])(t.beforeAddr).then((function(e){t.beforeAddrCmsRefCounter=e.data.length})).catch((function(e){t.beforeAddrCmsRefCounter="error",console.log(e.message)})).finally((function(){})),t.afterAddrCmsRefCounter="--",Object(c["C"])(t.afterAddr).then((function(e){t.afterAddrCmsRefCounter=e.data.length})).catch((function(e){t.afterAddrCmsRefCounter="error",console.log(e.message)})).finally((function(){}))},findDeployment:function(t){t.runningPodNum="--",Object(y["a"])(t.cluster,t.namespace,t.app).then((function(e){t.runningPodNum=e.data.replicas})).catch((function(e){t.runningPodNum=e.message}))}}},x=w,O=Object(b["a"])(x,g,_,!1,null,"c77d9b50",null),k=O.exports,j=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-dialog",{attrs:{title:"格式化内容",visible:t.dialogVisible,width:"50%",top:"5vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[n("div",{staticStyle:{"margin-top":"-50px",overflow:"auto"}},[n("div",{staticStyle:{"text-align":"center"}},[n("el-button",{attrs:{type:"text",size:"mini",icon:"el-icon-document-copy"},on:{click:t.copyToClipboard}},[t._v("一键复制内容")])],1),t._v(" "),n("pre",{staticStyle:{"white-space":"pre-wrap",border:"solid 1px #eee",padding:"5px","margin-top":"0","max-height":"600px","overflow-y":"auto"}},[t._v(t._s(t.exportData))])])]),t._v(" "),n("div",{staticStyle:{"text-align":"center","margin-top":"-10px"}},[n("el-button",{attrs:{type:"text",size:"mini"},on:{click:function(e){t.dialogVisible=!0}}},[t._v("查看格式化内容")]),t._v(" "),n("export-button",{attrs:{"table-ref":this.$refs.table001}})],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],ref:"table001",attrs:{data:t.tableData,"element-loading-text":"数据加载中...","default-sort":{prop:"status",order:"ascending"},border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{label:"环境",sortable:"",prop:"cluster"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v("\n        "+t._s(e.row.cluster)+" / "+t._s(e.row.namespace)+"\n      ")]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),t._v(" "),n("el-table-column",{attrs:{label:"持久存储"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",[t._v("\n          "+t._s(e.row.pvc.enable?e.row.pvc.name:"-")+"\n        ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"资源池",prop:"resourcePool"}}),t._v(" "),n("el-table-column",{attrs:{label:"应用信息",prop:"appRemark",width:"320px;"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticStyle:{"font-size":"12px","line-height":"14px"}},[e.row.appRemark?n("div",[t._v("\n            描述："+t._s(e.row.appRemark)+"\n          ")]):t._e(),t._v(" "),e.row.appOwner?n("div",[t._v("\n            Owner："+t._s(e.row.appOwner)+"\n          ")]):t._e()])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"状态",width:"100px",align:"center",filters:[{text:"正常",value:"enabled"},{text:"已迁移",value:"migrated"},{text:"待审核",value:"待审核"}],"filter-method":t.filterStatus},scopedSlots:t._u([{key:"default",fn:function(e){return["enabled"===e.row.status?n("el-tag",{attrs:{type:"success"}},[t._v("\n          "+t._s(t.convertStatus(e.row.status))+"\n        ")]):n("el-tag",{attrs:{type:"warning"}},[t._v("\n          "+t._s(t.convertStatus(e.row.status))+"\n        ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"配置实例数",align:"center",prop:"replicas",sortable:""}}),t._v(" "),n("el-table-column",{attrs:{label:"修改时间",prop:"updatedTime",sortable:""}}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"120px",fixed:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("router-link",{staticStyle:{"margin-left":"2px"},attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[n("span",{staticStyle:{color:"#409EFF","font-weight":"500"}},[t._v("发布流程页")])])]}}])})],1)],1)},S=[],C=n("51a9"),A=n("b562"),N={components:{ExportButton:p["a"]},data:function(){return{tableData:[],apps:{},tableLoading:!1,cmsUpdateVisible:!1,cmsUpdateOld:{cluster:"",namespace:"",app:"",text:""},cmsUpdateNew:{cluster:"",namespace:"",app:"",text:""},dialogVisible:!1}},computed:{exportData:function(){var t,e=[],n=Object(l["a"])(this.tableData);try{for(n.s();!(t=n.n()).done;){var a=t.value;e.push({cluster:a.cluster,namespace:a.namespace,app:a.app,appRemark:a.appRemark,appOwner:a.appOwner,status:a.status,replicas:a.replicas,pvc:a.pvc.enable?a.pvc.name:"-",resourcePool:a.resourcePool,updatedTime:a.updatedTime})}}catch(r){n.e(r)}finally{n.f()}return JSON.stringify(e,null,2)}},mounted:function(){this.loadAppAndPipeline()},methods:{loadAppAndPipeline:function(){var t=this;this.tableLoading=!0,Object(A["o"])({keyword:"",page:1,limit:1e4}).then((function(e){var n,a=Object(l["a"])(e.data.data);try{for(a.s();!(n=a.n()).done;){var r=n.value;t.apps[r.name]=r}}catch(o){a.e(o)}finally{a.f()}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1,t.loadTableData()}))},loadTableData:function(){var t=this;this.tableLoading=!0,Object(C["l"])({cluster:"k8s1",page:1,limit:1e4}).then((function(e){t.tableData=e.data.data;var n,a=Object(l["a"])(t.tableData);try{for(a.s();!(n=a.n()).done;){var r=n.value;r.appRemark="",r.appOwner="";var o=t.apps[r.app];o&&(r.appRemark=o.remark,o.admins&&(r.appOwner=o.admins.join(",")),r.appOwner=r.appOwner+(r.appOwner&&o.mainOwner?",":"")+o.mainOwner),r.resourcePool=r.schedule.node?r.schedule.node:"-"}}catch(i){a.e(i)}finally{a.f()}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},pipelinePage:function(t){this.$router.push({name:"cicd-app-deploy",query:{app:t.app}})},filterStatus:function(t,e){return e.status===t},convertStatus:function(t){switch(t){case"enabled":return"正常";case"audit":return"待审核";case"migrated":return"已迁移";default:return"未知"}},copyToClipboard:function(){var t=this,e=this.exportData;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var n=document.createElement("input");document.body.appendChild(n),n.setAttribute("value",e),n.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(n),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},$=N,D=Object(b["a"])($,j,S,!1,null,null,null),L=D.exports,R=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-row",{staticStyle:{"max-width":"1080px"},attrs:{gutter:20}},[n("el-col",{attrs:{span:18}},[n("el-form",[n("el-form-item",[n("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:5,maxRows:5},placeholder:"输入需要过滤的发布流程列表，内容格式：集群/运行环境/应用名，多个之间用换行分割"},model:{value:t.searchForm,callback:function(e){t.searchForm=e},expression:"searchForm"}})],1)],1)],1),t._v(" "),n("el-col",{attrs:{span:6}},[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.tableFilter}},[t._v("过滤")])],1)],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableData,"element-loading-text":"数据加载中...","default-sort":{prop:"createdTime",order:"ascending"},border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{type:"selection",width:"50"}}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"140px"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text"},on:{click:function(n){return t.deployUseK8s1Tag(e.row)}}},[t._v("使用k8s1版本发布\n        ")]),n("br"),t._v(" "),n("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text"}},[t._v("\n            发布流程页\n          ")])],1)]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"地址 (port / name)"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.ports,(function(a){return n("div",{staticStyle:{border:"1px #ccc solid",padding:"2px","margin-top":"3px"}},[n("div",{staticStyle:{display:"inline-block"}},[t._v("\n            "+t._s(a.value)+" / "+t._s(a.name)+"\n          ")]),t._v(" "),n("el-button",{staticStyle:{"padding-top":"0","padding-bottom":"0"},attrs:{type:"text"},on:{click:function(n){return t.updateCMSConfig(e.row,a.value)}}},[t._v("修改配置中心\n          ")])],1)}))}}])}),t._v(" "),n("el-table-column",{attrs:{label:"集群",prop:"cluster"}}),t._v(" "),n("el-table-column",{attrs:{label:"环境",filters:t.namespaceOptions,"filter-method":t.filterNamespace,prop:"namespace"}}),t._v(" "),n("el-table-column",{attrs:{label:"应用名",sortable:"",prop:"app"}}),t._v(" "),n("el-table-column",{attrs:{label:"状态",width:"100px",align:"center",filters:[{text:"正常",value:"enabled"},{text:"已迁移",value:"migrated"},{text:"待审核",value:"待审核"}],"filter-method":t.filterStatus},scopedSlots:t._u([{key:"default",fn:function(e){return["enabled"===e.row.status?n("el-tag",{attrs:{type:"success"}},[t._v("\n          "+t._s(t.convertStatus(e.row.status))+"\n        ")]):n("el-tag",{attrs:{type:"warning"}},[t._v("\n          "+t._s(t.convertStatus(e.row.status))+"\n        ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"配置实例数",align:"center",prop:"replicas"}}),t._v(" "),n("el-table-column",{attrs:{label:"创建人",property:"author"}}),t._v(" "),n("el-table-column",{attrs:{label:"修改时间",prop:"updatedTime",width:"100px"}})],1),t._v(" "),n("el-dialog",{attrs:{title:"地址修改确认",visible:t.cmsUpdateVisible,width:"860px"},on:{"update:visible":function(e){t.cmsUpdateVisible=e}}},[n("div",[n("el-descriptions",{attrs:{column:1,border:""}},[n("el-descriptions-item",[n("template",{slot:"label"},[n("b",[t._v(t._s(t.cmsUpdateOld.cluster))]),t._v(" "+t._s("/"+t.cmsUpdateOld.namespace+"/"+t.cmsUpdateOld.app)+" 地址：\n          ")]),t._v("\n          "+t._s(t.cmsUpdateOld.text)+"\n        ")],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[n("b",[t._v(t._s(t.cmsUpdateNew.cluster))]),t._v(" "+t._s("/"+t.cmsUpdateNew.namespace+"/"+t.cmsUpdateNew.app)+" 地址：\n          ")]),t._v("\n          "+t._s(t.cmsUpdateNew.text)+"\n        ")],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[n("b",{staticStyle:{"font-size":"1.2em"}},[t._v("配置中心修改：")])]),t._v("\n          "+t._s(t.cmsUpdateOld.text)+" → "+t._s(t.cmsUpdateNew.text)+"\n        ")],2)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.cmsUpdateVisible=!1}}},[t._v("取 消")]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:t.cmsPage}},[t._v("去配置中心修改")])],1)])],1)},T=[],F=(n("96cf"),n("3b8d")),E=(n("aef6"),n("a481"),n("ac6a"),n("5df3"),n("4f7f"),{components:{},data:function(){return{tableDataAll:[],tableData:[],searchForm:"",namespaceOptions:[],tableLoading:!1,cmsUpdateVisible:!1,cmsUpdateOld:{cluster:"",namespace:"",app:"",text:""},cmsUpdateNew:{cluster:"",namespace:"",app:"",text:""}}},computed:{},mounted:function(){this.loadTableData()},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(C["l"])({cluster:"k8s0",page:1,limit:1e4}).then((function(e){var n,a=new Set,r=Object(l["a"])(e.data.data);try{for(r.s();!(n=r.n()).done;){var o=n.value;a.add(o.namespace)}}catch(u){r.e(u)}finally{r.f()}var i,s=Object(l["a"])(a);try{for(s.s();!(i=s.n()).done;){var c=i.value;t.namespaceOptions.push({text:c,value:c})}}catch(u){s.e(u)}finally{s.f()}t.tableDataAll=e.data.data,t.tableFilter()})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},tableFilter:function(){if(this.searchForm&&this.searchForm.trim()){this.tableData=[],this.searchForm=this.searchForm.replace(/ /g,""),this.searchForm.endsWith("\n")||(this.searchForm+="\n"),console.log(this.searchForm);var t,e=Object(l["a"])(this.tableDataAll);try{for(e.s();!(t=e.n()).done;){var n=t.value;this.searchForm.indexOf("".concat(n.cluster,"/").concat(n.namespace,"/").concat(n.app,"\n"))>-1&&this.tableData.push(n)}}catch(a){e.e(a)}finally{e.f()}}else this.tableData=this.tableDataAll},findDeployment:function(){var t=Object(F["a"])(regeneratorRuntime.mark((function t(e,n,a){var r,o,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:r=null,o=Object(l["a"])(this.tableData),t.prev=2,o.s();case 4:if((i=o.n()).done){t.next=11;break}if(s=i.value,s.cluster!==e||s.namespace!==n||s.app!==a){t.next=9;break}return r=s,t.abrupt("break",11);case 9:t.next=4;break;case 11:t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](2),o.e(t.t0);case 16:return t.prev=16,o.f(),t.finish(16);case 19:if(null!==r){t.next=22;break}return this.$message.warning("找不到发布流程，应用：".concat(e," / ").concat(n," / ").concat(a)),t.abrupt("return");case 22:Object(y["a"])(e,n,a).then((function(t){r.extraAttr.deployTag=t.data.deployTag,r.extraAttr.runningPodNum=t.data.replicas})).catch((function(t){r.extraAttr.deployTag="--",r.extraAttr.runningPodNum=0,console.log(t.message)}));case 23:case"end":return t.stop()}}),t,this,[[2,13,16,19]])})));function e(e,n,a){return t.apply(this,arguments)}return e}(),filterStatus:function(t,e){return e.status===t},filterNamespace:function(t,e){return e.namespace===t},updateCMSConfig:function(t,e){var n=this;"k8s0"===t.cluster?Object(A["l"])("k8s0",t.namespace,t.app).then((function(a){n.cmsUpdateOld={},n.cmsUpdateNew={};var r,o=null,i=Object(l["a"])(a.data);try{for(i.s();!(r=i.n()).done;){var s=r.value;if(s.port===e){o=s;break}}}catch(c){i.e(c)}finally{i.f()}o?(n.cmsUpdateNew={cluster:"k8s0",namespace:t.namespace,app:t.app,text:o.clusterOuterAddress[0]},Object(A["l"])("k8s1",t.namespace,t.app).then((function(a){var r,o=null,i=Object(l["a"])(a.data);try{for(i.s();!(r=i.n()).done;){var s=r.value;if(s.port===e){o=s;break}}}catch(c){i.e(c)}finally{i.f()}o?(n.cmsUpdateOld={cluster:"k8s1",namespace:t.namespace,app:t.app,text:o.clusterOuterAddress[0]},n.cmsUpdateVisible=!0):n.$message.error("找不到地址")})).catch((function(t){n.$message.error(t.message)}))):n.$message.error("找不到地址")})).catch((function(t){n.$message.error(t.message)})):this.$message.warning("目前只支持k8s0环境")},deployUseK8s1Tag:function(t){var e=this;this.$prompt("确定要使用k8s1集群的版本发布应用 ".concat(t.app," 吗？，如果要跳过镜像构建，输入 true，否则输入 false"),"提示",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^(true|false)$/,inputErrorMessage:"输入值只能是 true 或者 false",showInput:!0,inputValue:"true",type:"warning"}).then((function(n){var a=n.value,r={cluster:t.cluster,namespace:t.namespace,app:t.app,oldCluster:"k8s1",skipImageBuild:a,forceCodeCompile:!1};Object(c["b"])(r).then((function(t){e.$message.success(t.data)})).catch((function(t){e.$message.error(t.message)}))})).catch((function(){}))},pipelinePage:function(t){this.$router.push({name:"cicd-app-deploy",query:{app:t.app}})},cmsPage:function(){var t="https://oss.foneshare.cn/cms/replace/preview/?profile=";t+="&src="+this.cmsUpdateOld.text+"&dst="+this.cmsUpdateNew.text,window.open(t,"_blank")},convertStatus:function(t){switch(t){case"enabled":return"正常";case"audit":return"待审核";case"migrated":return"已迁移";default:return"未知"}}}}),U=E,z=Object(b["a"])(U,R,T,!1,null,null,null),P=z.exports,B=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container pipeline-migrate-operation"},[n("el-row",{staticStyle:{"max-width":"1080px"},attrs:{gutter:20}},[n("el-col",{attrs:{span:18}},[n("el-form",[n("el-form-item",[n("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:5,maxRows:10},placeholder:"输入需要过滤的发布流程列表，内容格式：运行环境/应用名，多个之间用换行分割"},model:{value:t.searchForm,callback:function(e){t.searchForm=e},expression:"searchForm"}})],1)],1)],1),t._v(" "),n("el-col",{attrs:{span:6}},[n("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.loadTableData}},[t._v("查询")])],1)],1),t._v(" "),n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.tableLoading,expression:"tableLoading"}],attrs:{data:t.tableData,"row-class-name":t.tableRowClassName,"element-loading-text":"数据加载中...",border:"",fit:"","highlight-current-row":""}},[n("el-table-column",{attrs:{type:"index"}}),t._v(" "),n("el-table-column",{attrs:{type:"selection",width:"50"}}),t._v(" "),n("el-table-column",{attrs:{label:"集群",prop:"cluster"},scopedSlots:t._u([{key:"default",fn:function(e){return["k8s0"===e.row.cluster?n("div",{staticStyle:{color:"#FB5151","font-weight":"bold","font-size":"1.2em"}},[t._v(t._s(e.row.cluster)+" (新集群）")]):n("div",[t._v(t._s(e.row.cluster))])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"环境",prop:"namespace"}}),t._v(" "),n("el-table-column",{attrs:{label:"应用名",prop:"app"}}),t._v(" "),n("el-table-column",{attrs:{label:"操作",width:"140px"},scopedSlots:t._u([{key:"default",fn:function(e){return"k8s0"===e.row.cluster?[n("div",{},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0",color:"#FF9800"},attrs:{type:"text",size:"mini"},on:{click:function(n){return t.deployDialogShow(e.row)}}},[t._v("发布版本\n          ")]),t._v(" "),n("router-link",{attrs:{to:{name:"app-pipeline-execution-history",query:{keyword:e.row.app,namespace:e.row.namespace}},target:"_blank"}},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[t._v("\n              发布历史\n            ")])],1),t._v(" "),n("br"),t._v(" "),n("router-link",{attrs:{to:{name:"cicd-app-deploy",query:{app:e.row.app}},target:"_blank"}},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[t._v("\n              发布流程\n            ")])],1),t._v(" "),n("router-link",{attrs:{to:{name:"app-pipeline-edit",query:{pipelineId:e.row.id}},target:"_blank"}},[n("el-button",{staticStyle:{"margin-top":"0","padding-top":"0"},attrs:{type:"text",size:"mini"}},[t._v("\n              编辑流程\n            ")])],1)],1)]:void 0}}],null,!0)}),t._v(" "),n("el-table-column",{attrs:{label:"地址(port/name)"},scopedSlots:t._u([{key:"default",fn:function(e){return"k8s0"===e.row.cluster?t._l(e.row.ports,(function(a){return n("div",{staticStyle:{border:"1px #ccc solid",padding:"2px","margin-top":"3px"}},[n("div",{staticStyle:{display:"inline-block"}},[t._v("\n            "+t._s(a.value)+"/"+t._s(a.name)+"\n          ")]),t._v(" "),n("el-button",{staticStyle:{"padding-top":"0","padding-bottom":"0"},attrs:{type:"text",size:"mini"},on:{click:function(n){return t.updateCMSConfig(e.row,a.value)}}},[t._v("迁移地址\n          ")])],1)})):void 0}}],null,!0)}),t._v(" "),n("el-table-column",{attrs:{label:"状态",width:"100px",align:"center",filters:[{text:"正常",value:"enabled"},{text:"已迁移",value:"migrated"},{text:"待审核",value:"待审核"}],"filter-method":t.filterStatus},scopedSlots:t._u([{key:"default",fn:function(e){return["enabled"===e.row.status?n("el-tag",{attrs:{type:"success",size:"mini"}},[t._v("\n          "+t._s(t.convertStatus(e.row.status))+"\n        ")]):n("el-tag",{attrs:{type:"warning",size:"mini"}},[t._v("\n          "+t._s(t.convertStatus(e.row.status))+"\n        ")]),t._v(" "),n("div",[n("el-popover",{attrs:{placement:"top",width:"240"}},[n("p",[t._v("修改状态")]),t._v(" "),n("div",[n("el-button",{attrs:{size:"mini"},on:{click:function(n){return t.updatePipelineStatus(e.row,"enabled")}}},[t._v("正常")]),t._v(" "),n("el-button",{attrs:{size:"mini"},on:{click:function(n){return t.updatePipelineStatus(e.row,"migrated")}}},[t._v("已迁移")]),t._v(" "),n("el-button",{attrs:{size:"mini"},on:{click:function(n){return t.updatePipelineStatus(e.row,"disabled")}}},[t._v("禁用")])],1),t._v(" "),n("el-button",{attrs:{slot:"reference",type:"text",size:"mini"},slot:"reference"},[t._v("编辑")])],1)],1)]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"实例数(运行/配置)",align:"center",prop:"replicas"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.replicasIsSame(e.row.app,e.row.namespace)||"k8s0"!==e.row.cluster?n("div",[t._v("\n          "+t._s(e.row.extraAttr.runningPodNum)+" / "+t._s(e.row.replicas)+"\n        ")]):n("div",{staticStyle:{color:"#FB5151","font-size":"1.2em","font-weight":"bold"}},[t._v("\n          "+t._s(e.row.extraAttr.runningPodNum)+" / "+t._s(e.row.replicas)+"\n        ")])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"版本",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{staticStyle:{"font-size":"12px","font-weight":"bold"}},[t.versionIsSame(e.row.app,e.row.namespace)||"k8s0"!==e.row.cluster?n("div",[t._v("\n            "+t._s(e.row.extraAttr.deployTag)+"\n          ")]):n("div",{staticStyle:{color:"#FB5151"}},[t._v("\n            "+t._s(e.row.extraAttr.deployTag)+"\n          ")])])]}}])}),t._v(" "),n("el-table-column",{attrs:{label:"创建人",width:"100",property:"author"}})],1),t._v(" "),n("el-dialog",{attrs:{title:"使用k8s1版本发布当前环境",visible:t.deployOption.deployDialogVisible,width:"600px"},on:{"update:visible":function(e){return t.$set(t.deployOption,"deployDialogVisible",e)}}},[n("div",{domProps:{innerHTML:t._s("提示："+t.deployOption.prompt)}}),t._v(" "),n("div",{staticStyle:{"margin-top":"10px","margin-bottom":"-20px"}},[n("el-form",[n("el-form-item",[n("el-checkbox",{model:{value:t.deployOption.skipBuild,callback:function(e){t.$set(t.deployOption,"skipBuild",e)},expression:"deployOption.skipBuild"}},[t._v("跳过镜像构建")]),t._v(" "),n("el-checkbox",{model:{value:t.deployOption.forceCodeCompile,callback:function(e){t.$set(t.deployOption,"forceCodeCompile",e)},expression:"deployOption.forceCodeCompile"}},[t._v("强制编译代码")])],1)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){t.deployOption.deployDialogVisible=!1}}},[t._v("取 消")]),t._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:this.deployUseK8s1Tag}},[t._v("确 定")])],1)]),t._v(" "),n("el-dialog",{attrs:{title:"地址修改确认",visible:t.cmsUpdateVisible,width:"860px"},on:{"update:visible":function(e){t.cmsUpdateVisible=e}}},[n("div",[n("div",{staticStyle:{"padding-bottom":"3px",color:"#666"}},[t._v("格式： 集群/环境/应用名/端口")]),t._v(" "),n("el-descriptions",{attrs:{column:1,border:""}},[n("el-descriptions-item",[n("template",{slot:"label"},[n("b",[t._v(t._s(t.cmsUpdateOld.cluster+"/"+t.cmsUpdateOld.namespace+"/"))]),t._v(" "),n("span",{staticStyle:{color:"orangered"}},[t._v(t._s(t.cmsUpdateOld.app))]),t._v(" "),n("span",[t._v("/"+t._s(t.cmsUpdateOld.name))]),t._v(":\n          ")]),t._v("\n          "+t._s(t.cmsUpdateOld.addr)+"\n        ")],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[n("b",[t._v(t._s(t.cmsUpdateNew.cluster+"/"+t.cmsUpdateNew.namespace+"/"))]),t._v(" "),n("span",{staticStyle:{color:"orangered"}},[t._v(t._s(t.cmsUpdateNew.app)+" ")]),t._v(" "),n("span",[t._v("/"+t._s(t.cmsUpdateNew.name))]),t._v(":\n          ")]),t._v("\n          "+t._s(t.cmsUpdateNew.addr)+"\n        ")],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[n("b",{staticStyle:{"font-size":"1.2em"}},[t._v("地址修改：")])]),t._v("\n          "+t._s(t.cmsUpdateOld.addr)+" → "+t._s(t.cmsUpdateNew.addr)+"\n        ")],2)],1)],1),t._v(" "),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(e){return t.$message.info("请本地修改git代码")}}},[t._v("改JavaConsole")]),t._v(" "),n("el-button",{on:{click:function(e){return t.$message.info("请本地修改git代码")}}},[t._v("改Traefik")]),t._v(" "),n("a",{attrs:{href:"https://oss.foneshare.cn/cms/replace/preview/?profile=&&src="+this.cmsUpdateOld.addr+"&dst="+this.cmsUpdateNew.addr,target:"_blank"}},[n("el-button",{attrs:{type:"primary"}},[t._v("改配置中心")])],1)],1)])],1)},q=[],M={components:{},data:function(){return{tableData:[],searchForm:"",tableLoading:!1,cmsUpdateVisible:!1,cmsUpdateOld:{cluster:"",namespace:"",app:"",name:"",addr:""},cmsUpdateNew:{cluster:"",namespace:"",app:"",name:"",addr:""},deployOption:{deployDialogVisible:!1,prompt:"",cluster:"",namespace:"",app:"",skipBuild:!0,forceCodeCompile:!1}}},computed:{},mounted:function(){this.searchForm&&this.loadTableData()},methods:{loadTableData:function(){var t=this;this.tableLoading=!0,Object(c["q"])(this.searchForm).then((function(e){t.tableData=e.data.data;var n,a=Object(l["a"])(t.tableData);try{for(a.s();!(n=a.n()).done;){var r=n.value;r.extraAttr.deployTag="?",r.extraAttr.runningPodNum="?",t.findDeployment(r.cluster,r.namespace,r.app)}}catch(o){a.e(o)}finally{a.f()}})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.tableLoading=!1}))},tableRowClassName:function(t){var e=t.row;t.rowIndex;return e.cluster+"-row"},tableFilter:function(){if(this.searchForm&&this.searchForm.trim()){this.tableData=[],this.searchForm=this.searchForm.replace(/ /g,""),this.searchForm.endsWith("\n")||(this.searchForm+="\n"),console.log(this.searchForm);var t,e=Object(l["a"])(this.tableDataAll);try{for(e.s();!(t=e.n()).done;){var n=t.value;this.searchForm.indexOf("".concat(n.cluster,"/").concat(n.namespace,"/").concat(n.app,"\n"))>-1&&this.tableData.push(n)}}catch(a){e.e(a)}finally{e.f()}}else this.tableData=this.tableDataAll},versionIsSame:function(t,e){var n,a="",r="",o=Object(l["a"])(this.tableData);try{for(o.s();!(n=o.n()).done;){var i=n.value;"k8s0"===i.cluster&&i.namespace===e&&i.app===t&&(a=i.extraAttr.deployTag),"k8s1"===i.cluster&&i.namespace===e&&i.app===t&&(r=i.extraAttr.deployTag)}}catch(s){o.e(s)}finally{o.f()}return""!==a&&""!==r&&a===r},replicasIsSame:function(t,e){var n,a="",r="",o="",i="",s=Object(l["a"])(this.tableData);try{for(s.s();!(n=s.n()).done;){var c=n.value;"k8s0"===c.cluster&&c.namespace===e&&c.app===t&&(a=c.replicas,o=c.runningPodNum),"k8s1"===c.cluster&&c.namespace===e&&c.app===t&&(r=c.replicas,i=c.runningPodNum)}}catch(u){s.e(u)}finally{s.f()}return a===r&&o===i},findDeployment:function(){var t=Object(F["a"])(regeneratorRuntime.mark((function t(e,n,a){var r,o,i,s;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:r=null,o=Object(l["a"])(this.tableData),t.prev=2,o.s();case 4:if((i=o.n()).done){t.next=11;break}if(s=i.value,s.cluster!==e||s.namespace!==n||s.app!==a){t.next=9;break}return r=s,t.abrupt("break",11);case 9:t.next=4;break;case 11:t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](2),o.e(t.t0);case 16:return t.prev=16,o.f(),t.finish(16);case 19:if(null!==r){t.next=22;break}return this.$message.warning("找不到发布流程，应用：".concat(e," / ").concat(n," / ").concat(a)),t.abrupt("return");case 22:Object(y["a"])(e,n,a).then((function(t){r.extraAttr.deployTag=t.data.deployTag,r.extraAttr.runningPodNum=t.data.replicas})).catch((function(t){console.log(t.message)}));case 23:case"end":return t.stop()}}),t,this,[[2,13,16,19]])})));function e(e,n,a){return t.apply(this,arguments)}return e}(),filterStatus:function(t,e){return e.status===t},updateCMSConfig:function(t,e){var n=this;"k8s0"===t.cluster?Object(A["l"])("k8s0",t.namespace,t.app).then((function(a){n.cmsUpdateOld={},n.cmsUpdateNew={};var r,o=null,i=Object(l["a"])(a.data);try{for(i.s();!(r=i.n()).done;){var s=r.value;if(s.port===e){o=s;break}}}catch(c){i.e(c)}finally{i.f()}o?(n.cmsUpdateNew={cluster:"k8s0",namespace:t.namespace,app:t.app,name:o.name,addr:o.clusterOuterAddress[0]},Object(A["l"])("k8s1",t.namespace,t.app).then((function(a){var r,o=null,i=Object(l["a"])(a.data);try{for(i.s();!(r=i.n()).done;){var s=r.value;if(s.port===e){o=s;break}}}catch(c){i.e(c)}finally{i.f()}o?(n.cmsUpdateOld={cluster:"k8s1",namespace:t.namespace,app:t.app,name:o.name,addr:o.clusterOuterAddress[0]},n.cmsUpdateVisible=!0):n.$message.error("找不到地址")})).catch((function(t){n.$message.error(t.message)}))):n.$message.error("找不到地址")})).catch((function(t){n.$message.error(t.message)})):this.$message.warning("目前只支持k8s0环境")},deployDialogShow:function(t){this.deployOption.prompt="确定要使用k8s1集群的版本发布应用吗？应用：".concat(t.cluster,"/").concat(t.namespace,"/").concat(t.app),this.deployOption.deployDialogVisible=!0,this.deployOption.cluster=t.cluster,this.deployOption.namespace=t.namespace,this.deployOption.app=t.app},deployUseK8s1Tag:function(){var t=this,e={cluster:this.deployOption.cluster,namespace:this.deployOption.namespace,app:this.deployOption.app,oldCluster:"k8s1",skipImageBuild:this.deployOption.skipBuild,forceCodeCompile:this.deployOption.forceCodeCompile};Object(c["b"])(e).then((function(e){t.$message.success(e.data)})).catch((function(e){t.$message.error(e.message)}))},updatePipelineStatus:function(t,e){var n=this,a={};a.id=t.id,a.status=e,console.log(a),Object(C["o"])(a).then((function(a){n.$message.success("修改成功"),t.status=e})).catch((function(t){n.$message.error(t.message)})).finally((function(){}))},convertStatus:function(t){switch(t){case"enabled":return"正常";case"audit":return"待审核";case"migrated":return"已迁移";case"disabled":return"禁用";default:return"未知"}}}},I=M,V=(n("ad8b"),Object(b["a"])(I,B,q,!1,null,null,null)),H=V.exports,W=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("div",[n("div",{staticStyle:{float:"left",width:"600px"}},[n("el-form",[n("el-form-item",{attrs:{label:"请输入发布流程列表"}},[n("el-input",{attrs:{type:"textarea",autosize:"",autosize:{minRows:8,maxRows:12}},model:{value:t.searchForm,callback:function(e){t.searchForm=e},expression:"searchForm"}})],1),t._v(" "),n("el-form-item",[n("el-popconfirm",{attrs:{title:"确定要克隆发布流程吗？"},on:{confirm:t.query}},[n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{slot:"reference",type:"primary",size:"mini"},slot:"reference"},[t._v("打开克隆页面")])],1),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.changeStatus("enabled")}}},[t._v("批量改成【可用】")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.changeStatus("disabled")}}},[t._v("批量改成【禁用】")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.changeStatus("migrated")}}},[t._v("批量改成【已迁移】")]),t._v(" "),n("br"),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.queryReplicas()}}},[t._v("查询副本数")]),t._v(" "),n("el-popconfirm",{attrs:{title:"确定要修改副本数吗？"},on:{confirm:t.updateReplicas}},[n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{slot:"reference",type:"default",size:"mini"},slot:"reference"},[t._v("修改副本数")])],1),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.queryAddr()}}},[t._v("查询地址")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.queryMigrateAddr()}}},[t._v("获取迁移地址")]),t._v(" "),n("br"),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.createClickHouseSql()}}},[t._v("Tomcat流量查询SQL生成")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.createClickHouseSql2()}}},[t._v("FCP流量查询SQL生成")]),t._v(" "),n("el-button",{directives:[{name:"loading",rawName:"v-loading",value:t.btnLoading,expression:"btnLoading"}],attrs:{type:"default",size:"mini"},on:{click:function(e){return t.trafficAnalysis()}}},[t._v("Tomcat流量查询")])],1)],1)],1),t._v(" "),n("div",{staticStyle:{float:"left",width:"500px","padding-left":"5px"}},[n("el-form",[n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"内容格式示例:"}}),t._v(" "),n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:""}},[n("div",{staticStyle:{"line-height":"1.2em","background-color":"#eee",height:"180px"}},[t._v("\n            # 克隆页面、修改状态、查询副本数："),n("br"),t._v("\n            k8s0/fstest/fs-devops-console"),n("br"),t._v("\n            k8s0/fstest/fs-service-console"),n("br"),t._v("\n            k8s0/fstest/fs-stone-admin"),n("br"),n("br"),t._v("\n            # 修改副本数："),n("br"),t._v("\n            k8s0/fstest/fs-devops-console/1"),n("br"),t._v("\n            k8s0/fstest/fs-service-console/1"),n("br"),t._v("\n            k8s0/fstest/fs-stone-admin/1\n          ")])])],1)],1)]),t._v(" "),t.searchResult?n("div",{staticStyle:{"margin-top":"20px","max-width":"1080px",clear:"both"}},[n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[t._v("操作结果")])]),t._v(" "),n("div",[n("pre",{staticStyle:{"white-space":"pre-wrap","word-wrap":"break-word","overflow-wrap":"break-word","font-size":"12px"}},[t._v(t._s(this.searchResult))])])])],1):t._e()])},K=[],J=n("768b"),G=(n("28a5"),{components:{},mounted:function(){},beforeDestroy:function(){},computed:{},data:function(){return{searchForm:"",searchResult:"",btnLoading:!1}},methods:{clonePipeline:function(t){var e=this.$router.resolve({name:"app-pipeline-edit",query:{operate:"clone",pipelineId:t.id,targetCluster:"k8s0",targetNamespace:t.namespace}});window.open(e.href,"_blank")},query:function(){var t=this;this.btnLoading=!0,Object(c["s"])(this.searchForm).then((function(e){t.searchResult=e.data.message,e.data.data.length>0&&(t.$message.info("即将在新窗口打开发布流程编辑页面"),setTimeout((function(){var n,a=Object(l["a"])(e.data.data);try{for(a.s();!(n=a.n()).done;){var r=n.value;t.clonePipeline(r)}}catch(o){a.e(o)}finally{a.f()}}),3e3))})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},changeStatus:function(t){var e=this;Object(c["u"])(t,this.searchForm).then((function(t){e.searchResult=t.data.message})).catch((function(t){e.$message.error(t.message)})).finally((function(){e.btnLoading=!1}))},queryReplicas:function(){var t=this;this.btnLoading=!0,Object(c["v"])(this.searchForm).then((function(e){t.searchResult=e.data.data.join("\n")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},updateReplicas:function(){var t=this;this.btnLoading=!0,Object(c["w"])(this.searchForm).then((function(e){t.searchResult=e.data.data.join("\n")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},queryAddr:function(){var t=this;this.btnLoading=!0,Object(c["o"])(this.searchForm).then((function(e){t.searchResult=e.data.join("\n")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},queryMigrateAddr:function(){var t=this;this.btnLoading=!0,Object(c["p"])(this.searchForm).then((function(e){t.searchResult=e.data.join("\n")})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))},createClickHouseSql:function(){this.btnLoading=!0;var t,e={},n=Object(l["a"])(this.searchForm.split("\n"));try{for(n.s();!(t=n.n()).done;){var a=t.value;if(""!==a.trim()){var r=a.split("/"),o=Object(J["a"])(r,3),i=o[0],s=o[1],c=o[2];e[i]||(e[i]={}),e[i][s]||(e[i][s]=[]),e[i][s].push(c)}}}catch(b){n.e(b)}finally{n.f()}var u=[];for(var p in e)for(var d in e[p]){var f=e[p][d].join("','"),m="SELECT app, profile as namespace, COUNT(*) as count\n                     FROM logger.tomcat_access\n                     WHERE toDate(toDateTime(_time_second_)) > toDate(now() - 5 * 24 * 3600)\n                       AND cluster = 'foneshare-".concat(p,"'\n                       AND profile = '").concat(d,"'\n                       AND app IN ('").concat(f,"')\n                     GROUP BY (app, profile);  ");u.push(m)}this.searchResult=u.join("\n"),this.btnLoading=!1},createClickHouseSql2:function(){this.btnLoading=!0;var t,e=[],n=Object(l["a"])(this.searchForm.split("\n"));try{for(n.s();!(t=n.n()).done;){var a=t.value;a.trim()&&e.push(a.trim())}}catch(i){n.e(i)}finally{n.f()}var r="'"+e.join("','")+"'",o="SELECT server_ip, COUNT(*) as count\n                 FROM biz_log.log_cep_dist\n                 WHERE toDate(toDateTime(stamp)) > toDate(now() - 5 * 24 * 3600)\n                   AND server_ip IN (".concat(r,")\n                 GROUP BY server_ip;  ");this.searchResult=o,this.btnLoading=!1},trafficAnalysis:function(){var t=this;this.btnLoading=!0,Object(c["r"])(this.searchForm).then((function(e){t.searchResult=JSON.stringify(e.data,null,2)})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.btnLoading=!1}))}}}),Y=G,Q=Object(b["a"])(Y,W,K,!1,null,null,null),X=Q.exports,Z={components:{ClusterMigratePipelineBatchOperation:X,ClusterMigrateOperation:H,ClusterMigrateNew:P,ClusterMigrateOld:L,ClusterMigrateLbSplitApibus:k,ClusterMigrateLbSplit:h},props:{activeName:{type:String,default:""}},mounted:function(){var t=this.$route.query.tab;t||(t="app-gc"),this.activeTab=t},computed:{},data:function(){return{activeTab:this.activeName}},methods:{handleClick:function(t,e){var n=t.name;this.$router.push({query:Object(o["a"])(Object(o["a"])({},this.$route.query),{},{tab:n})})}}},tt=Z,et=Object(b["a"])(tt,a,r,!1,null,null,null);e["default"]=et.exports},d2c8:function(t,e,n){var a=n("aae3"),r=n("be13");t.exports=function(t,e,n){if(a(e))throw TypeError("String#"+n+" doesn't accept regex!");return String(r(t))}},da37:function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(e){return t.copyToClipboard()}}},[n("i",{staticClass:"el-icon-document-copy"}),t._v(" "),this.buttonText?n("span",[t._v(t._s(this.buttonText))]):t._e()])},r=[],o={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var t=this,e=this.text;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var n=document.createElement("input");document.body.appendChild(n),n.setAttribute("value",e),n.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(n),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},i=o,s=n("2877"),l=Object(s["a"])(i,a,r,!1,null,null,null);e["a"]=l.exports},e0b8:function(t,e,n){"use strict";var a=n("7726"),r=n("5ca1"),o=n("2aba"),i=n("dcbc"),s=n("67ab"),l=n("4a59"),c=n("f605"),u=n("d3f4"),p=n("79e5"),d=n("5cc5"),f=n("7f20"),m=n("5dbc");t.exports=function(t,e,n,b,v,h){var g=a[t],_=g,y=v?"set":"add",w=_&&_.prototype,x={},O=function(t){var e=w[t];o(w,t,"delete"==t||"has"==t?function(t){return!(h&&!u(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return h&&!u(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,n){return e.call(this,0===t?0:t,n),this})};if("function"==typeof _&&(h||w.forEach&&!p((function(){(new _).entries().next()})))){var k=new _,j=k[y](h?{}:-0,1)!=k,S=p((function(){k.has(1)})),C=d((function(t){new _(t)})),A=!h&&p((function(){var t=new _,e=5;while(e--)t[y](e,e);return!t.has(-0)}));C||(_=e((function(e,n){c(e,_,t);var a=m(new g,e,_);return void 0!=n&&l(n,v,a[y],a),a})),_.prototype=w,w.constructor=_),(S||A)&&(O("delete"),O("has"),v&&O("get")),(A||j)&&O(y),h&&w.clear&&delete w.clear}else _=b.getConstructor(e,t,v,y),i(_.prototype,n),s.NEED=!0;return f(_,t),x[t]=_,r(r.G+r.W+r.F*(_!=g),x),h||b.setStrong(_,t,v),_}}}]);