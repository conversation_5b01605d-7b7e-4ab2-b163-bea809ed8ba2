(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7d5d4bfb"],{"2d07":function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"app-container"},[n("div",{staticStyle:{"text-align":"center","margin-bottom":"10px"}},[n("el-form",{staticClass:"demo-form-inline",attrs:{inline:!0,model:t.searchForm},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.findApp(e)},submit:function(t){t.preventDefault()}}},[n("el-form-item",[n("el-input",{staticStyle:{width:"460px"},attrs:{placeholder:t.inputPlaceholder},model:{value:t.searchForm.address,callback:function(e){t.$set(t.searchForm,"address","string"===typeof e?e.trim():e)},expression:"searchForm.address"}},[n("el-select",{staticStyle:{width:"180px"},attrs:{slot:"prepend",placeholder:"请选择"},on:{change:t.addrTypeChange},slot:"prepend",model:{value:t.searchForm.addrType,callback:function(e){t.$set(t.searchForm,"addrType",e)},expression:"searchForm.addrType"}},t._l(t.addrTypeOptions,(function(t){return n("el-option",{key:t.name,attrs:{label:t.label,value:t.name}})})),1)],1)],1),t._v(" "),n("el-form-item",[n("el-button",{attrs:{type:"primary",icon:"el-icon-search"},on:{click:t.findApp}},[t._v("查询")])],1)],1),t._v(" "),t.app.name?n("el-card",{staticClass:"box-card",staticStyle:{width:"460px",margin:"30px auto","text-align":"left"}},[n("el-form",{attrs:{"label-width":"80px"}},[n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"运行环境:"}},[t._v("\n          "+t._s(t.app.namespace)+"\n        ")]),t._v(" "),n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"k8s集群:"}},[t._v("\n          "+t._s(t.app.cluster)+"\n        ")]),t._v(" "),n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"应用名称:"}},[t._v("\n          "+t._s(t.app.name)+"\n        ")]),t._v(" "),t.app.pod?n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"Pod名称:"}},[t._v("\n          "+t._s(t.app.pod)+"\n        ")]):t._e()],1),t._v(" "),n("div",{staticStyle:{"margin-top":"10px"}},[n("el-button",{staticClass:"el-icon-position",staticStyle:{margin:"0 20px"},attrs:{type:"text"},on:{click:t.pipeline_page}},[t._v("发布流程页面\n        ")]),t._v(" "),n("el-button",{staticClass:"el-icon-menu",attrs:{type:"text"},on:{click:t.pod_page}},[t._v("实例管理页面\n        ")])],1)],1):t._e()],1)])},r=[],o=(n("7f7f"),n("c1ab")),c=n("4ad4"),u={mounted:function(){var t=this.addrTypeOptions[0];this.inputPlaceholder=t.placeholder,this.searchForm.addrType=t.name},computed:{},components:{AppAddress:c["a"]},data:function(){return{loading:!1,searchForm:{addrType:"",address:""},inputPlaceholder:"",addrTypeOptions:[{name:"nodeVIP",label:"访问地址查应用",placeholder:"请输入应用访问地址，格式为 IP:PORT"},{name:"podIP",label:"Pod IP查实例",placeholder:"请输入pod ip"}],app:{}}},methods:{findApp:function(){var t=this,e=this.searchForm.address;if(e){this.loading=!0;var n="nodeVIP"===this.searchForm.addrType?o["h"]:o["j"];n(this.searchForm.address).then((function(e){t.app=e.data})).catch((function(e){t.$message.error(e.message),t.app={}})).finally((function(){t.loading=!1}))}else this.$message.error("地址不能为空")},addrTypeChange:function(t){var e=this.addrTypeOptions.filter((function(e){return e.name===t}));this.inputPlaceholder=e&&e[0]?e[0].placeholder:""},pipeline_page:function(){this.$router.push({name:"cicd-app-deploy",query:{app:this.app.name}})},pod_page:function(){this.$router.push({name:"pod-index",query:{cluster:this.app.cluster,namespace:this.app.namespace,app:this.app.name}})}}},i=u,s=n("2877"),p=Object(s["a"])(i,a,r,!1,null,null,null);e["default"]=p.exports},"4ad4":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-address-wrapper"},[n("el-card",{staticClass:"box-card"},[n("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[n("span",[t._v("访问地址")]),t._v(" "),n("span",{staticStyle:{display:"inline-block","margin-left":"20px","font-size":"13px"}},[t._v("\n        ( 应用："+t._s(this.app)+" | 环境："+t._s(this.namespace)+" | 集群："+t._s(this.cluster)+" )\n      ")])]),t._v(" "),n("div",[n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticStyle:{width:"100%","margin-top":"10px"},attrs:{data:t.address}},[n("el-table-column",{attrs:{prop:"name",width:"180",label:"端口名称"}}),t._v(" "),n("el-table-column",{attrs:{prop:"port",width:"100",label:"端口号"}}),t._v(" "),n("el-table-column",{attrs:{prop:"protocol",width:"100",label:"协议"}}),t._v(" "),n("el-table-column",{attrs:{label:"访问地址"},scopedSlots:t._u([{key:"default",fn:function(e){return t._l(e.row.addresses,(function(e,a){return n("p",{staticStyle:{margin:"8px"}},[n("b",{staticStyle:{"padding-right":"10px"}},[t._v("地址"+t._s(a+1)+":")]),t._v(t._s(e)+"\n              "),n("clipboard-icon",{staticStyle:{"margin-left":"10px"},attrs:{text:e,"button-text":""}})],1)}))}}])}),t._v(" "),n("el-table-column",{attrs:{prop:"remark",label:"备注"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("div",{domProps:{innerHTML:t._s(e.row.remark)}})]}}])})],1)],1)])],1)},r=[],o=n("b562"),c=n("da37"),u={name:"AppAddress",components:{ClipboardIcon:c["a"]},props:{cluster:{type:String,require:!0},namespace:{type:String,require:!0},app:{type:String,require:!0}},data:function(){return{loading:!1,address:[]}},watch:{cluster:function(t){this.showAddress()},namespace:function(t){this.showAddress()},app:function(t){this.showAddress()}},computed:{},mounted:function(){this.showAddress()},methods:{showAddress:function(){var t=this;this.cluster?this.namespace?this.app?(this.loading=!0,Object(o["l"])(this.cluster,this.namespace,this.app).then((function(e){t.address=e.data})).catch((function(e){t.$message.error(e.message)})).finally((function(){t.loading=!1}))):this.$message.warning("缺少参数 app"):this.$message.warning("缺少参数 namespace"):this.$message.warning("缺少参数 cluster")}}},i=u,s=(n("de9b"),n("2877")),p=Object(s["a"])(i,a,r,!1,null,null,null);e["a"]=p.exports},b562:function(t,e,n){"use strict";n.d(e,"o",(function(){return r})),n.d(e,"b",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"k",(function(){return u})),n.d(e,"i",(function(){return i})),n.d(e,"d",(function(){return s})),n.d(e,"h",(function(){return p})),n.d(e,"g",(function(){return d})),n.d(e,"l",(function(){return l})),n.d(e,"n",(function(){return m})),n.d(e,"f",(function(){return f})),n.d(e,"e",(function(){return h})),n.d(e,"c",(function(){return b})),n.d(e,"j",(function(){return v})),n.d(e,"q",(function(){return g})),n.d(e,"m",(function(){return y})),n.d(e,"p",(function(){return O}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/app/search",method:"get",params:t})}function o(){return Object(a["a"])({url:"/v1/app/apps-with-env",method:"get"})}function c(){return Object(a["a"])({url:"/v1/app/all",method:"get"})}function u(){return Object(a["a"])({url:"/v1/app/names",method:"get"})}function i(t){return Object(a["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function s(t){return Object(a["a"])({url:"/v1/app",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/v1/app",method:"put",data:t})}function d(t){return Object(a["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function l(t,e,n){return Object(a["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:n}})}function m(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function f(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function h(t){return Object(a["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function v(t,e){return Object(a["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function g(t,e){return Object(a["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function y(t,e){return Object(a["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function O(){return Object(a["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}},b674:function(t,e,n){},c1ab:function(t,e,n){"use strict";n.d(e,"h",(function(){return r})),n.d(e,"j",(function(){return o})),n.d(e,"z",(function(){return c})),n.d(e,"A",(function(){return u})),n.d(e,"c",(function(){return i})),n.d(e,"f",(function(){return s})),n.d(e,"E",(function(){return p})),n.d(e,"G",(function(){return d})),n.d(e,"y",(function(){return l})),n.d(e,"a",(function(){return m})),n.d(e,"e",(function(){return f})),n.d(e,"D",(function(){return h})),n.d(e,"F",(function(){return b})),n.d(e,"x",(function(){return v})),n.d(e,"H",(function(){return g})),n.d(e,"k",(function(){return y})),n.d(e,"d",(function(){return O})),n.d(e,"B",(function(){return j})),n.d(e,"i",(function(){return _})),n.d(e,"g",(function(){return x})),n.d(e,"s",(function(){return w})),n.d(e,"v",(function(){return k})),n.d(e,"w",(function(){return C})),n.d(e,"o",(function(){return S})),n.d(e,"p",(function(){return T})),n.d(e,"t",(function(){return $})),n.d(e,"u",(function(){return A})),n.d(e,"b",(function(){return F})),n.d(e,"q",(function(){return P})),n.d(e,"r",(function(){return q})),n.d(e,"n",(function(){return I})),n.d(e,"C",(function(){return E})),n.d(e,"m",(function(){return V})),n.d(e,"l",(function(){return N}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/tool/find-app-by-address",method:"get",params:{address:t}})}function o(t){return Object(a["a"])({url:"/v1/tool/find-pod-by-ip",method:"get",params:{address:t}})}function c(t){return Object(a["a"])({url:"/v1/tool/scan-jar",method:"get",params:t})}function u(t){return Object(a["a"])({url:"/v1/tool/scan-tomcat-version",method:"get",params:t})}function i(){return Object(a["a"])({url:"/v1/tool/app-restart/output",method:"get"})}function s(t){return Object(a["a"])({url:"/v1/tool/app-restart/create",method:"post",data:t})}function p(t){return Object(a["a"])({url:"/v1/tool/app-restart/start",method:"post",data:t})}function d(t){return Object(a["a"])({url:"/v1/tool/app-restart/stop",method:"post",data:t})}function l(t){return Object(a["a"])({url:"/v1/tool/app-restart/remove",method:"post",data:t})}function m(){return Object(a["a"])({url:"/v1/tool/app-deploy/output",method:"get"})}function f(t,e,n,r,o,c,u,i){return Object(a["a"])({url:"/v1/tool/app-deploy/create?type=".concat(t,"&forceCodeCompile=").concat(o,"&fixVersion=").concat(e,"&suffixVersion=").concat(n,"&message=").concat(r,"&dependencyCheck=").concat(c,"&parentPom=").concat(u),method:"post",data:i})}function h(t){return Object(a["a"])({url:"/v1/tool/app-deploy/start",method:"post",data:t})}function b(t){return Object(a["a"])({url:"/v1/tool/app-deploy/stop",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/v1/tool/app-deploy/remove",method:"post",data:t})}function g(t){return Object(a["a"])({url:"/v1/tool/yaml-export",method:"post",data:t})}function y(t,e,n,r){return Object(a["a"])({url:"/v1/tool/helm-chart-build?cluster=".concat(t,"&namespace=").concat(e,"&app=").concat(n,"&overrideNamespace=").concat(r),method:"post"})}function O(t,e,n,r,o){return Object(a["a"])({url:"/v1/tool/app-version-snapshot?cluster=".concat(t,"&namespace=").concat(e,"&version=").concat(n,"&remark=").concat(r,"&dryRun=").concat(o),method:"post"})}function j(){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/search",method:"get"})}function _(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/detail?id=".concat(t),method:"get"})}function x(t){return Object(a["a"])({url:"/v1/tool/app-version-snapshot/delete?id=".concat(t),method:"delete"})}function w(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-clone",method:"post",data:t})}function k(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-query",method:"post",data:t})}function C(t){return Object(a["a"])({url:"/v1/tool/pipeline-replica-update",method:"post",data:t})}function S(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query",method:"post",data:t})}function T(t){return Object(a["a"])({url:"/v1/tool/migrate-addr-query2",method:"post",data:t})}function $(t){return Object(a["a"])({url:"/v1/tool/pipeline-batch-resource-update",method:"post",data:t})}function A(t,e){return Object(a["a"])({url:"/v1/tool/pipeline-batch-update-status?status="+t,method:"post",data:e})}function F(t){return Object(a["a"])({url:"/v1/tool/app-deploy-with-old-k8s-tag",method:"get",params:t})}function P(t){return Object(a["a"])({url:"/v1/tool/migrate/pipeline-search",method:"post",data:t})}function q(t){return Object(a["a"])({url:"/v1/tool/migrate/traffic-analysis?day=7",method:"post",data:t})}function I(t,e,n){return Object(a["a"])({url:"/v1/tool/load-cms-profile-configs?cluster=".concat(t,"&namespace=").concat(e,"&op=").concat(n),method:"get"})}function E(t){return Object(a["a"])({url:"/v1/tool/search-cms-configs?keyword=".concat(t),method:"get"})}function V(t,e,n){return Object(a["a"])({url:"/v1/tool/load-app-by-lb-addr?cluster=".concat(t,"&namespace=").concat(e,"&lbAddr=").concat(n),method:"get"})}function N(){return Object(a["a"])({url:"/v1/tool/load-apibus-addr",method:"get"})}},da37:function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(e){return t.copyToClipboard()}}},[n("i",{staticClass:"el-icon-document-copy"}),t._v(" "),this.buttonText?n("span",[t._v(t._s(this.buttonText))]):t._e()])},r=[],o={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var t=this,e=this.text;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var n=document.createElement("input");document.body.appendChild(n),n.setAttribute("value",e),n.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(n),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},c=o,u=n("2877"),i=Object(u["a"])(c,a,r,!1,null,null,null);e["a"]=i.exports},de9b:function(t,e,n){"use strict";n("b674")}}]);