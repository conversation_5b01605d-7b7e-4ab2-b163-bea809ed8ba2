(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-008573e1"],{"03b6":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-descriptions",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"margin-top",attrs:{column:t.columns,size:"mini",border:"",labelClassName:"app-desc-label"}},[n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        应用\n        "),n("el-tooltip",{attrs:{content:"应用名称",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("div",{staticStyle:{"min-width":"160px"}},[t._v("\n        "+t._s(t.appMeta.name)+"\n        "),n("clipboard-icon",{attrs:{text:t.appMeta.name}}),t._v(" "),t.appMeta.level?n("el-tooltip",{attrs:{content:"服务等级",placement:"top"}},[n("el-tag",{staticStyle:{"margin-left":"10px","font-size":"12px"},attrs:{size:"mini",type:"warning",title:"应用级别"}},[t._v(t._s(t.appMeta.level))])],1):t._e()],1)],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("第一负责人\n        "),n("el-tooltip",{attrs:{content:"应用的主负责人。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v("\n      "+t._s(t.appMeta.mainOwner?t.appMeta.mainOwner:"--")+"\n      "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appEditPage}},[t._v("编辑\n      ")])],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        负责人\n        "),n("el-tooltip",{attrs:{content:"应用的负责人。可以编辑应用信息、配置应用发布权限、创建应用发布流程，对应用进行发布、接收应用告警通知等",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("span",[n("el-tooltip",{attrs:{content:t.appMeta.owners&&t.appMeta.owners.length>0?t.appMeta.owners.join(","):"没有配置负责人",placement:"top"}},[n("span",[t._v("\n            "+t._s(t.appMeta.owners&&t.appMeta.owners.length>0?t.appMeta.owners.join(",").length>24?t.appMeta.owners.join(",").substring(0,24)+"...":t.appMeta.owners.join(","):"--")+"\n          ")])])],1)],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        发布权限\n        "),n("el-tooltip",{attrs:{content:"如果配置了权限，则拥有权限的同学才能发布。如果没有配置，则所有人都可发布。",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),t.appMeta.orgs&&t.appMeta.orgs.length>0?n("div",{staticStyle:{display:"inline-block"}},[n("div",{staticStyle:{"max-width":"400px"}},[t._v("\n          部门："+t._s(t.appMeta.orgs.join(","))+"\n          "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.orgPage}},[t._v("查看部门成员\n          ")])],1)]):n("div",{staticStyle:{display:"inline-block"}},[t._v("-任何人-")]),t._v(" "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appAuthPage}},[t._v("编辑\n      ")])],2),t._v(" "),n("el-descriptions-item",{attrs:{span:2}},[n("template",{slot:"label"},[t._v("\n        发布时间窗口\n        "),n("el-tooltip",{attrs:{content:"如果配置了时间窗口，则只能在时间窗口范围内才能发布。如果没有配置，则任意时间都可以发布。",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v(" "),n("div",[!1===this.$settings.timeWindow.open?[t._v("\n          - 系统已关闭时间窗口限制 -\n        ")]:t.appMeta.timeWindow&&t.appMeta.timeWindow.length>0?[n("el-tooltip",{attrs:{effect:"light",placement:"top"}},[n("div",{attrs:{slot:"content"},slot:"content"},[n("pre",[t._v(t._s(t.appMeta.timeWindowDesc))])]),t._v(" "),n("span",{staticStyle:{display:"inline-block"}},[t._v("\n              "+t._s(t.appMeta.timeWindowDesc.substring(0,60).replaceAll("\n","  ")+(t.appMeta.timeWindowDesc.length>60?"...":""))+"\n            ")])]),t._v(" "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appEditPage}},[t._v("编辑\n          ")]),t._v(" "),this.excludeNamespaces.length>0?n("div",{staticStyle:{color:"#d45e0c","font-weight":"bold","max-width":"800px"}},[t._v("以下环境不受限制："+t._s(this.excludeNamespaces.join(", ")))]):t._e()]:[t._v("-任何时间-\n          "),n("el-button",{staticStyle:{"font-size":"12px",padding:"0","margin-left":"8px"},attrs:{type:"text"},on:{click:t.appEditPage}},[t._v("编辑\n          ")])]],2)],2),t._v(" "),n("el-descriptions-item",[n("template",{slot:"label"},[t._v("\n        描述\n        "),n("el-tooltip",{attrs:{content:"应用描述信息",placement:"top"}},[n("svg-icon",{attrs:{"icon-class":"question","class-name":"question-icon-size"}})],1)],1),t._v("\n      "+t._s(t.appMeta.remark)+"\n    ")],2)],1)],1)},r=[],i=(n("c5f6"),n("b562")),o=n("da37"),s={name:"pipeline-app",components:{ClipboardIcon:o["a"]},props:{app:{type:String,default:"",required:!0},columns:{type:Number,default:2,required:!1}},mounted:function(){this.loadApp()},watch:{app:function(t){this.loadApp()}},computed:{excludeNamespaces:function(){return this.$settings.timeWindow&&this.$settings.timeWindow.excludeNamespaces?this.$settings.timeWindow.excludeNamespaces:[]}},data:function(){return{loading:!1,appMeta:{}}},methods:{loadApp:function(){var t=this;this.app?(this.loading=!0,Object(i["i"])(this.app).then((function(e){t.appMeta=e.data})).catch((function(e){t.$message.error(e.message),t.appMeta={}})).finally((function(){t.loading=!1}))):console.log(this.app)},appAuthPage:function(){var t=this.$router.resolve({name:"app-permission",query:{app:this.app}}).href;window.open(t,"_blank")},orgPage:function(){var t=this.$router.resolve({name:"auth-org",query:{}}).href;window.open(t,"_blank")},appEditPage:function(){var t=this.$router.resolve({name:"app-list",query:{showEditDialog:"true",app:this.app}}).href;window.open(t,"_blank")}}},c=s,p=(n("9df5"),n("2877")),u=Object(p["a"])(c,a,r,!1,null,null,null);e["a"]=u.exports},"11e9":function(t,e,n){var a=n("52a7"),r=n("4630"),i=n("6821"),o=n("6a99"),s=n("69a8"),c=n("c69a"),p=Object.getOwnPropertyDescriptor;e.f=n("9e1e")?p:function(t,e){if(t=i(t),e=o(e,!0),c)try{return p(t,e)}catch(n){}if(s(t,e))return r(!a.f.call(t,e),t[e])}},"181b":function(t,e,n){},"1c4c":function(t,e,n){"use strict";var a=n("9b43"),r=n("5ca1"),i=n("4bf8"),o=n("1fa8"),s=n("33a4"),c=n("9def"),p=n("f1ae"),u=n("27ee");r(r.S+r.F*!n("5cc5")((function(t){Array.from(t)})),"Array",{from:function(t){var e,n,r,l,d=i(t),f="function"==typeof this?this:Array,m=arguments.length,h=m>1?arguments[1]:void 0,v=void 0!==h,b=0,g=u(d);if(v&&(h=a(h,m>2?arguments[2]:void 0,2)),void 0==g||f==Array&&s(g))for(e=c(d.length),n=new f(e);e>b;b++)p(n,b,v?h(d[b],b):d[b]);else for(l=g.call(d),n=new f;!(r=l.next()).done;b++)p(n,b,v?o(l,h,[r.value,b],!0):r.value);return n.length=b,n}})},"4f7f":function(t,e,n){"use strict";var a=n("c26b"),r=n("b39a"),i="Set";t.exports=n("e0b8")(i,(function(t){return function(){return t(this,arguments.length>0?arguments[0]:void 0)}}),{add:function(t){return a.def(r(this,i),t=0===t?0:t,t)}},a)},"57c3":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[this.maintainIsOpen?n("div",{staticStyle:{margin:"0 auto",padding:"10px"}},[n("el-alert",{attrs:{title:"",closable:!1,type:"warning"}},[n("template",{slot:"title"},[n("div",{staticStyle:{"line-height":"18px",color:"orangered","font-weight":"bold"}},[n("div",{staticStyle:{"font-size":"16px"}},[n("span",{staticStyle:{color:"orangered"}},[n("i",{staticClass:"el-icon-warning"})]),t._v(" "),n("span",{staticStyle:{"padding-left":"5px"}},[t._v("系统提示")])]),t._v(" "),n("div",{staticStyle:{"padding-top":"10px","font-size":"14px"},domProps:{innerHTML:t._s(this.maintainDesc)}})])])],2)],1):n("div",{staticStyle:{display:"none"}})])},r=[],i={name:"maintain-alert",props:{maintainType:{type:String,required:!0}},data:function(){return{}},watch:{},computed:{maintainIsOpen:function(){return"CD"===this.maintainType.toUpperCase()?this.$settings.maintain.cd.open:"CI"===this.maintainType.toUpperCase()&&this.$settings.maintain.ci.open},maintainDesc:function(){return"CD"===this.maintainType.toUpperCase()?this.$settings.maintain.cd.desc:"CI"===this.maintainType.toUpperCase()?this.$settings.maintain.ci.desc:""}},mounted:function(){},methods:{}},o=i,s=n("2877"),c=Object(s["a"])(o,a,r,!1,null,null,null);e["a"]=c.exports},"5dbc":function(t,e,n){var a=n("d3f4"),r=n("8b97").set;t.exports=function(t,e,n){var i,o=e.constructor;return o!==n&&"function"==typeof o&&(i=o.prototype)!==n.prototype&&a(i)&&r&&r(t,i),t}},"5df3":function(t,e,n){"use strict";var a=n("02f4")(!0);n("01f9")(String,"String",(function(t){this._t=String(t),this._i=0}),(function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=a(e,n),this._i+=t.length,{value:t,done:!1})}))},"67ab":function(t,e,n){var a=n("ca5a")("meta"),r=n("d3f4"),i=n("69a8"),o=n("86cc").f,s=0,c=Object.isExtensible||function(){return!0},p=!n("79e5")((function(){return c(Object.preventExtensions({}))})),u=function(t){o(t,a,{value:{i:"O"+ ++s,w:{}}})},l=function(t,e){if(!r(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!i(t,a)){if(!c(t))return"F";if(!e)return"E";u(t)}return t[a].i},d=function(t,e){if(!i(t,a)){if(!c(t))return!0;if(!e)return!1;u(t)}return t[a].w},f=function(t){return p&&m.NEED&&c(t)&&!i(t,a)&&u(t),t},m=t.exports={KEY:a,NEED:!1,fastKey:l,getWeak:d,onFreeze:f}},"6a4c":function(t,e,n){"use strict";n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/gitlab/ci-file/create",method:"post",data:t})}function i(t){window.open("/api/v1/gitlab/ci-file/download?fileId="+t)}function o(t){return Object(a["a"])({url:"/v1/gitlab/tags",method:"post",data:t})}},"6d2d":function(t,e,n){},"71df":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[t.showHistory?n("div",{staticStyle:{"margin-bottom":"10px",height:"30px",overflow:"hidden"}},[n("label",{staticStyle:{display:"inline-block",width:"80px",color:"#999","font-size":"14px","padding-right":"12px","text-align":"right"}},[t._v("访问历史")]),t._v(" "),t._l(t.recentApps,(function(e){return n("el-button",{staticStyle:{"font-weight":"bold","margin-bottom":"5px","font-size":"14px",padding:"7px"},attrs:{size:"mini",type:"primary",plain:e!==t.currApp},on:{click:function(n){return t.changeCurrAppByBtn(e)}}},[t._v(t._s(e)+"\n    ")])}))],2):t._e(),t._v(" "),n("div",[n("el-form",[n("el-form-item",{staticStyle:{"margin-bottom":"0"},attrs:{label:"选择应用","label-width":"80px"}},[n("el-select",{staticStyle:{width:"100%","margin-bottom":"10px"},attrs:{filterable:"",placeholder:"请选择应用"},on:{change:t.changeCurrAppBySelector},model:{value:t.currApp,callback:function(e){t.currApp=e},expression:"currApp"}},t._l(t.apps,(function(t){return n("el-option",{key:t,attrs:{label:t,value:t}})})),1)],1)],1)],1),t._v(" "),t.currApp&&t.showDetail?n("pipeline-app",{attrs:{app:this.currApp,columns:4}}):t._e()],1)},r=[],i=n("db72"),o=n("c24f"),s=n("b562"),c=n("03b6"),p={name:"app-selector2",components:{PipelineApp:c["a"]},props:{showDetail:{type:Boolean,default:!1},showHistory:{type:Boolean,default:!0},updateHistory:{type:Boolean,default:!1}},data:function(){return{apps:[],recentApps:[],currApp:"",reloadHistory:!1}},watch:{currApp:function(t){this.appChange()}},computed:{},mounted:function(){var t=this.$route.query.app;t&&(this.currApp=t),this.loadApps(),this.loadRecentApps()},methods:{appChange:function(){var t=this;this.$router.push({query:Object(i["a"])(Object(i["a"])({},this.$route.query),{},{tab:this.currTab})}),this.updateHistory&&Object(o["e"])(this.currApp).then((function(e){t.reloadHistory&&t.loadRecentApps()})).catch((function(t){})),this.$emit("change",this.currApp)},changeCurrAppByBtn:function(t){this.reloadHistory=!1,this.currApp=t},changeCurrAppBySelector:function(){this.reloadHistory=!0},loadRecentApps:function(){var t=this;Object(o["a"])().then((function(e){t.recentApps=e.data.recentApps,!t.currApp&&t.recentApps.length>0&&(t.currApp=t.recentApps[0])})).catch((function(t){console.log(t)}))},loadApps:function(){var t=this;Object(s["k"])().then((function(e){t.apps=e.data})).catch((function(e){t.$message.error("加载应用数据出错！ "+e.message)}))}}},u=p,l=n("2877"),d=Object(l["a"])(u,a,r,!1,null,null,null);e["a"]=d.exports},"76fe":function(t,e,n){"use strict";n.d(e,"k",(function(){return r})),n.d(e,"i",(function(){return i})),n.d(e,"a",(function(){return o})),n.d(e,"e",(function(){return s})),n.d(e,"b",(function(){return c})),n.d(e,"f",(function(){return p})),n.d(e,"c",(function(){return u})),n.d(e,"g",(function(){return l})),n.d(e,"h",(function(){return d})),n.d(e,"d",(function(){return f})),n.d(e,"l",(function(){return m})),n.d(e,"j",(function(){return h}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/job/search",method:"post",data:t})}function i(t){return Object(a["a"])({url:"/v1/job/image-options",method:"get",params:{pipelineIds:t}})}function o(t){return Object(a["a"])({url:"/v1/job/build-image",method:"post",data:t})}function s(t){return Object(a["a"])({url:"/v1/job/deploy-app",method:"post",data:t})}function c(t){return Object(a["a"])({url:"/v1/job/build-and-deploy",method:"post",data:t})}function p(t,e,n,r){return Object(a["a"])({url:"/v1/job/deploy-app-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:r}})}function u(t,e,n,r){return Object(a["a"])({url:"/v1/job/build-image-with-current-version",method:"post",params:{cluster:t,namespace:e,app:n,remark:r}})}function l(t){return Object(a["a"])({url:"/v1/job/detail",method:"get",params:{id:t}})}function d(t){return Object(a["a"])({url:"/v1/job/tasks",method:"get",params:{jobId:t}})}function f(t){return Object(a["a"])({url:"/v1/job/cancel",method:"put",data:{id:t}})}function m(t,e){return Object(a["a"])({url:"/v1/job/update-status?id=".concat(t,"&status=").concat(e),method:"put"})}function h(t){return Object(a["a"])({url:"/v1/job/redo",method:"post",data:{id:t}})}},"84d4":function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"cicd-menu-tabs"},[n("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":t.handleClick},model:{value:t.currTab,callback:function(e){t.currTab=e},expression:"currTab"}},[n("el-tab-pane",{attrs:{label:"aaa",name:"app-deploy"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"guide"}}),t._v("应用发布")],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"app-deploy-history"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("发布记录")],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"image-build"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像构建")],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"image-build-history"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"record"}}),t._v("构建记录")],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"image-list"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"image"}}),t._v("镜像列表")],1)]),t._v(" "),n("el-tab-pane",{attrs:{name:"doc",disabled:!0}},[n("span",{staticStyle:{color:"#909399"},attrs:{slot:"label"},slot:"label"},[n("svg-icon",{staticStyle:{"margin-right":"2px"},attrs:{"icon-class":"help"}}),t._v(" "),n("a",{attrs:{target:"_blank",href:"https://wiki.firstshare.cn/pages/viewpage.action?pageId=331320474"}},[t._v("查看使用手册")])],1)])],1)],1)},r=[],i=(n("7f7f"),{props:{tabName:{type:String,default:""}},mounted:function(){this.currTab=this.tabName},data:function(){return{currTab:""}},computed:{},methods:{handleClick:function(t,e){"app-deploy"===t.name?this.$router.push({name:"cicd-app-deploy"}):"app-deploy-history"===t.name?this.$router.push({name:"cicd-app-deploy-history"}):"image-build"===t.name?this.$router.push({name:"cicd-image-build"}):"image-build-history"===t.name?this.$router.push({name:"cicd-image-build-history"}):"image-list"===t.name?this.$router.push({name:"cicd-image-list"}):"doc"===t.name||this.$message.error("未知操作")}}}),o=i,s=(n("df01"),n("2877")),c=Object(s["a"])(o,a,r,!1,null,null,null);e["a"]=c.exports},8504:function(t,e,n){"use strict";n.d(e,"g",(function(){return r})),n.d(e,"a",(function(){return i})),n.d(e,"h",(function(){return o})),n.d(e,"c",(function(){return s})),n.d(e,"b",(function(){return c})),n.d(e,"i",(function(){return p})),n.d(e,"d",(function(){return u})),n.d(e,"f",(function(){return l})),n.d(e,"e",(function(){return d}));var a=n("b775");function r(t,e){return Object(a["a"])({url:"/v1/k8s/deployment/list",method:"get",params:{cluster:t,namespace:e}})}function i(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail",method:"get",params:{cluster:t,namespace:e,app:n}})}function o(t){return Object(a["a"])({url:"/v1/k8s/deployment/update-resource",method:"post",data:t})}function s(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/detail/whole",method:"get",params:{cluster:t,namespace:e,app:n}})}function c(t){return Object(a["a"])({url:"/v1/k8s/deployment/redeploy",method:"put",params:t})}function p(t){return Object(a["a"])({url:"/v1/k8s/deployment/use-recreate-deploy-strategy",method:"put",params:t})}function u(t,e,n){return Object(a["a"])({url:"/v1/k8s/deployment/replicaSet/list",method:"get",params:{cluster:t,namespace:e,app:n}})}function l(t){return Object(a["a"])({url:"/v1/k8s/deployment/scale",method:"put",params:t})}function d(t,e,n,r,i){return Object(a["a"])({url:"/v1/k8s/deployment/rollback",method:"put",params:{cluster:t,namespace:e,app:n,revision:r,deployTag:i||""}})}},"8b97":function(t,e,n){var a=n("d3f4"),r=n("cb7c"),i=function(t,e){if(r(t),!a(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,a){try{a=n("9b43")(Function.call,n("11e9").f(Object.prototype,"__proto__").set,2),a(t,[]),e=!(t instanceof Array)}catch(r){e=!0}return function(t,n){return i(t,n),e?t.__proto__=n:a(t,n),t}}({},!1):void 0),check:i}},9093:function(t,e,n){var a=n("ce10"),r=n("e11e").concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return a(t,r)}},"9df5":function(t,e,n){"use strict";n("181b")},a6ce:function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[t._l(this.parentPoms.normals,(function(e){return n("el-option",{key:e.ID,attrs:{label:e.showName,value:e.name}},[n("span",[t._v(t._s(e.showName)+" ")]),t._v(" "),n("span",{staticStyle:{"padding-left":"20px",color:"#8492a6","font-size":"13px"}},[n("label",{staticStyle:{color:"orangered","font-size":"12px"}},[t._v(t._s(e.remark))])])])})),t._v(" "),n("el-divider",{staticStyle:{margin:"0"}},[n("span",{staticStyle:{"font-size":"12px",color:"#777"}},[t._v("相关文档")])]),t._v(" "),t._m(0),t._v(" "),this.parentPoms.archives.length>0?n("el-divider",[n("span",{staticStyle:{"font-size":"12px",color:"#777"}},[t._v("归档父pom (不推荐使用）")])]):t._e(),t._v(" "),n("div",{staticStyle:{"margin-top":"-10px"}},t._l(this.parentPoms.archives,(function(e){return n("el-option",{key:e.ID,attrs:{label:e.showName,value:e.name}},[n("span",[t._v(t._s(e.showName)+" ")]),t._v(" "),n("span",{staticStyle:{"padding-left":"20px",color:"#8492a6","font-size":"13px"}},[n("label",{staticStyle:{color:"orangered","font-size":"12px"}},[t._v(t._s(e.remark))])])])})),1)],2)},r=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{"margin-top":"-10px","font-size":"12px",color:"#777","margin-left":"20px","line-height":"1.5em"}},[n("div",[t._v("• 如果你有 JAR 包需要部署到 Maven 仓库，请点击 "),n("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:"https://www.fxiaoke.com/XV/UI/Home#crm/list/=/object_zKc15__c",target:"_blank"}},[t._v("链接")])]),t._v(" "),n("div",[t._v("• 如果你需要在父 POM 中升级 JAR 包版本，请点击 "),n("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:"https://www.fxiaoke.com/XV/UI/Home#crm/list/=/parent_pom_update__c",target:"_blank"}},[t._v("链接")])]),t._v(" "),n("div",[t._v("• 如果你想查看不同父 POM 所管理的 JAR 包版本，请点击 "),n("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:"https://git.firstshare.cn/JavaCommon/parent-pom/-/blob/master/version-diff.md",target:"_blank"}},[t._v("链接")])]),t._v(" "),n("div",[t._v("• 如果你想了解父 POM 的版本规范及其所管理的 JAR 包升级规范，请点击 "),n("a",{staticStyle:{color:"#3a8ee6"},attrs:{href:"https://365.kdocs.cn/l/cj4XYsEkBQ4r",target:"_blank"}},[t._v("链接")])])])}],i=n("2d63"),o=n("edaf"),s={name:"parent-pom-options",components:{},data:function(){return{parentPoms:{normals:[],archives:[]}}},computed:{},mounted:function(){this.loadParentPoms()},methods:{loadParentPoms:function(){var t=this;Object(o["c"])({}).then((function(e){t.parentPoms={normals:[],archives:[]};var n,a=e.data,r=Object(i["a"])(a);try{for(r.s();!(n=r.n()).done;){var o=n.value;o.enable&&(o.isArchive?t.parentPoms.archives.push(o):t.parentPoms.normals.push(o))}}catch(s){r.e(s)}finally{r.f()}console.log(t.parentPoms)})).catch((function(e){t.$message.error("获取父pom失败，"+e.message)}))}}},c=s,p=n("2877"),u=Object(p["a"])(c,a,r,!1,null,null,null);e["a"]=u.exports},aa77:function(t,e,n){var a=n("5ca1"),r=n("be13"),i=n("79e5"),o=n("fdef"),s="["+o+"]",c="​",p=RegExp("^"+s+s+"*"),u=RegExp(s+s+"*$"),l=function(t,e,n){var r={},s=i((function(){return!!o[t]()||c[t]()!=c})),p=r[t]=s?e(d):o[t];n&&(r[n]=p),a(a.P+a.F*s,"String",r)},d=l.trim=function(t,e){return t=String(r(t)),1&e&&(t=t.replace(p,"")),2&e&&(t=t.replace(u,"")),t};t.exports=l},b144:function(t,e,n){"use strict";function a(t){return JSON.parse(JSON.stringify(t))}function r(t){if(!t||!(t instanceof Date))return"";var e=t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+t.getMinutes()+":"+t.getSeconds();return e}function i(t){return"isCoreApp"===t?"核心服务":"onlyDeployTag"===t?"只允许部署Tag":"addSysctlKeepalive"===t?"调整内核参数":"skyWalkingAgent"===t?"性能跟踪":"appLogToKafka"===t?"接入ClickHouse日志":"buildUseRuntimeJDK"===t?"镜像JDK版本编译代码":"jvmGcLog"===t?"GC日志":t}n.d(e,"a",(function(){return a})),n.d(e,"c",(function(){return r})),n.d(e,"b",(function(){return i}))},b39a:function(t,e,n){var a=n("d3f4");t.exports=function(t,e){if(!a(t)||t._t!==e)throw TypeError("Incompatible receiver, "+e+" required!");return t}},b562:function(t,e,n){"use strict";n.d(e,"o",(function(){return r})),n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return o})),n.d(e,"k",(function(){return s})),n.d(e,"i",(function(){return c})),n.d(e,"d",(function(){return p})),n.d(e,"h",(function(){return u})),n.d(e,"g",(function(){return l})),n.d(e,"l",(function(){return d})),n.d(e,"n",(function(){return f})),n.d(e,"f",(function(){return m})),n.d(e,"e",(function(){return h})),n.d(e,"c",(function(){return v})),n.d(e,"j",(function(){return b})),n.d(e,"q",(function(){return g})),n.d(e,"m",(function(){return _})),n.d(e,"p",(function(){return y}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/app/search",method:"get",params:t})}function i(){return Object(a["a"])({url:"/v1/app/apps-with-env",method:"get"})}function o(){return Object(a["a"])({url:"/v1/app/all",method:"get"})}function s(){return Object(a["a"])({url:"/v1/app/names",method:"get"})}function c(t){return Object(a["a"])({url:"/v1/app/detail",method:"get",params:{name:t}})}function p(t){return Object(a["a"])({url:"/v1/app",method:"post",data:t})}function u(t){return Object(a["a"])({url:"/v1/app",method:"put",data:t})}function l(t){return Object(a["a"])({url:"/v1/app/",method:"delete",params:{name:t}})}function d(t,e,n){return Object(a["a"])({url:"/v1/app/address",method:"get",params:{cluster:t,namespace:e,app:n}})}function f(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"get",params:{app:t}})}function m(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"post",data:t})}function h(t){return Object(a["a"])({url:"/v1/app/create-bugfix-branch",method:"post",data:t})}function v(t){return Object(a["a"])({url:"/v1/app/git-tag",method:"delete",data:t})}function b(t,e){return Object(a["a"])({url:"/v1/app/git-tag/find",method:"get",params:{git_url:t,search_name:e}})}function g(t,e){return Object(a["a"])({url:"/v1/app/permission",method:"put",data:{app:t,orgs:e}})}function _(t,e){return Object(a["a"])({url:"/v1/app/git-modules",method:"get",params:{app:t,pipelineId:e||""}})}function y(){return Object(a["a"])({url:"/v1/app/sync-from-cmdb",method:"get"})}},bcbd:function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[t.jobs&&t.jobs.length>0?n("el-alert",{attrs:{type:"info"}},[n("template",{slot:"title"},[n("span",[t._v(t._s(t.jobTitle)+"：")]),t._v(" "),t._l(t.jobs,(function(e){return n("el-link",{staticStyle:{margin:"0 10px"},attrs:{type:"primary",underline:!1},on:{click:function(n){return t.jobPage(e.id)}}},["CD"===t.jobType?n("div",[t._v("\n          "+t._s(e.params.namespace)+" ("+t._s(e.params.cluster)+")\n        ")]):n("div",[t._v("\n          "+t._s(e.id)+"\n        ")])])}))],2)],2):t._e()],1)},r=[],i=n("76fe"),o={name:"job-runner-alert",props:{app:{type:String,default:""},jobType:{type:String,required:!0},jobTitle:{type:String,default:"运行中的任务"}},data:function(){return{jobs:[]}},computed:{},mounted:function(){this.loadJobs()},watch:{app:function(t){this.jobs=[],this.loadJobs()}},methods:{loadJobs:function(){var t=this;if(this.app){var e={params:{},app:this.app,status:["WAIT","RUNNING"],type:this.jobType,page:1,limit:100};Object(i["k"])(e).then((function(e){t.jobs=e.data.data})).catch((function(t){console.log("load job fail, "+t.message)}))}},jobPage:function(t){var e="";"CI"===this.jobType?e="cicd-image-build-detail":"CD"===this.jobType?e="cicd-app-deploy-detail":this.$message.error("未知的任务类型："+this.jobType);var n=this.$router.resolve({name:e,query:{jobId:t}});window.open(n.href,"_blank")}}},s=o,c=n("2877"),p=Object(c["a"])(s,a,r,!1,null,null,null);e["a"]=p.exports},c26b:function(t,e,n){"use strict";var a=n("86cc").f,r=n("2aeb"),i=n("dcbc"),o=n("9b43"),s=n("f605"),c=n("4a59"),p=n("01f9"),u=n("d53b"),l=n("7a56"),d=n("9e1e"),f=n("67ab").fastKey,m=n("b39a"),h=d?"_s":"size",v=function(t,e){var n,a=f(e);if("F"!==a)return t._i[a];for(n=t._f;n;n=n.n)if(n.k==e)return n};t.exports={getConstructor:function(t,e,n,p){var u=t((function(t,a){s(t,u,e,"_i"),t._t=e,t._i=r(null),t._f=void 0,t._l=void 0,t[h]=0,void 0!=a&&c(a,n,t[p],t)}));return i(u.prototype,{clear:function(){for(var t=m(this,e),n=t._i,a=t._f;a;a=a.n)a.r=!0,a.p&&(a.p=a.p.n=void 0),delete n[a.i];t._f=t._l=void 0,t[h]=0},delete:function(t){var n=m(this,e),a=v(n,t);if(a){var r=a.n,i=a.p;delete n._i[a.i],a.r=!0,i&&(i.n=r),r&&(r.p=i),n._f==a&&(n._f=r),n._l==a&&(n._l=i),n[h]--}return!!a},forEach:function(t){m(this,e);var n,a=o(t,arguments.length>1?arguments[1]:void 0,3);while(n=n?n.n:this._f){a(n.v,n.k,this);while(n&&n.r)n=n.p}},has:function(t){return!!v(m(this,e),t)}}),d&&a(u.prototype,"size",{get:function(){return m(this,e)[h]}}),u},def:function(t,e,n){var a,r,i=v(t,e);return i?i.v=n:(t._l=i={i:r=f(e,!0),k:e,v:n,p:a=t._l,n:void 0,r:!1},t._f||(t._f=i),a&&(a.n=i),t[h]++,"F"!==r&&(t._i[r]=i)),t},getEntry:v,setStrong:function(t,e,n){p(t,e,(function(t,n){this._t=m(t,e),this._k=n,this._l=void 0}),(function(){var t=this,e=t._k,n=t._l;while(n&&n.r)n=n.p;return t._t&&(t._l=n=n?n.n:t._t._f)?u(0,"keys"==e?n.k:"values"==e?n.v:[n.k,n.v]):(t._t=void 0,u(1))}),n?"entries":"values",!n,!0),l(e)}}},c5f6:function(t,e,n){"use strict";var a=n("7726"),r=n("69a8"),i=n("2d95"),o=n("5dbc"),s=n("6a99"),c=n("79e5"),p=n("9093").f,u=n("11e9").f,l=n("86cc").f,d=n("aa77").trim,f="Number",m=a[f],h=m,v=m.prototype,b=i(n("2aeb")(v))==f,g="trim"in String.prototype,_=function(t){var e=s(t,!1);if("string"==typeof e&&e.length>2){e=g?e.trim():d(e,3);var n,a,r,i=e.charCodeAt(0);if(43===i||45===i){if(n=e.charCodeAt(2),88===n||120===n)return NaN}else if(48===i){switch(e.charCodeAt(1)){case 66:case 98:a=2,r=49;break;case 79:case 111:a=8,r=55;break;default:return+e}for(var o,c=e.slice(2),p=0,u=c.length;p<u;p++)if(o=c.charCodeAt(p),o<48||o>r)return NaN;return parseInt(c,a)}}return+e};if(!m(" 0o1")||!m("0b1")||m("+0x1")){m=function(t){var e=arguments.length<1?0:t,n=this;return n instanceof m&&(b?c((function(){v.valueOf.call(n)})):i(n)!=f)?o(new h(_(e)),n,m):_(e)};for(var y,j=n("9e1e")?p(h):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),x=0;j.length>x;x++)r(h,y=j[x])&&!r(m,y)&&l(m,y,u(h,y));m.prototype=v,v.constructor=m,n("2aba")(a,f,m)}},da37:function(t,e,n){"use strict";var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticStyle:{display:"inline-block","margin-left":"10px",color:"#409EFF",cursor:"pointer"},on:{click:function(e){return t.copyToClipboard()}}},[n("i",{staticClass:"el-icon-document-copy"}),t._v(" "),this.buttonText?n("span",[t._v(t._s(this.buttonText))]):t._e()])},r=[],i={name:"ClipboardIcon",props:{text:{type:String,require:!0},buttonText:{type:String,default:""}},data:function(){return{}},watch:{},computed:{},mounted:function(){},methods:{copyToClipboard:function(){var t=this,e=this.text;e?navigator.clipboard.writeText(e).then((function(){t.$message.success("复制成功")})).catch((function(){var n=document.createElement("input");document.body.appendChild(n),n.setAttribute("value",e),n.select(),document.execCommand("copy")&&document.execCommand("copy"),document.body.removeChild(n),t.$message.success("复制成功")})):this.$message.warning("内容为空")}}},o=i,s=n("2877"),c=Object(s["a"])(o,a,r,!1,null,null,null);e["a"]=c.exports},df01:function(t,e,n){"use strict";n("6d2d")},e0b8:function(t,e,n){"use strict";var a=n("7726"),r=n("5ca1"),i=n("2aba"),o=n("dcbc"),s=n("67ab"),c=n("4a59"),p=n("f605"),u=n("d3f4"),l=n("79e5"),d=n("5cc5"),f=n("7f20"),m=n("5dbc");t.exports=function(t,e,n,h,v,b){var g=a[t],_=g,y=v?"set":"add",j=_&&_.prototype,x={},w=function(t){var e=j[t];i(j,t,"delete"==t||"has"==t?function(t){return!(b&&!u(t))&&e.call(this,0===t?0:t)}:"get"==t?function(t){return b&&!u(t)?void 0:e.call(this,0===t?0:t)}:"add"==t?function(t){return e.call(this,0===t?0:t),this}:function(t,n){return e.call(this,0===t?0:t,n),this})};if("function"==typeof _&&(b||j.forEach&&!l((function(){(new _).entries().next()})))){var O=new _,S=O[y](b?{}:-0,1)!=O,k=l((function(){O.has(1)})),A=d((function(t){new _(t)})),E=!b&&l((function(){var t=new _,e=5;while(e--)t[y](e,e);return!t.has(-0)}));A||(_=e((function(e,n){p(e,_,t);var a=m(new g,e,_);return void 0!=n&&c(n,v,a[y],a),a})),_.prototype=j,j.constructor=_),(k||E)&&(w("delete"),w("has"),v&&w("get")),(E||S)&&w(y),b&&j.clear&&delete j.clear}else _=h.getConstructor(e,t,v,y),o(_.prototype,n),s.NEED=!0;return f(_,t),x[t]=_,r(r.G+r.W+r.F*(_!=g),x),b||h.setStrong(_,t,v),_}},edaf:function(t,e,n){"use strict";n.d(e,"c",(function(){return r})),n.d(e,"b",(function(){return i})),n.d(e,"a",(function(){return o}));var a=n("b775");function r(t){return Object(a["a"])({url:"/v1/sys/parent-pom",method:"get",params:t})}function i(t){return Object(a["a"])({url:"/v1/sys/parent-pom",method:"post",data:t})}function o(t){return Object(a["a"])({url:"/v1/sys/parent-pom",method:"delete",params:t})}},f1ae:function(t,e,n){"use strict";var a=n("86cc"),r=n("4630");t.exports=function(t,e,n){e in t?a.f(t,e,r(0,n)):t[e]=n}},fdef:function(t,e){t.exports="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff"}}]);