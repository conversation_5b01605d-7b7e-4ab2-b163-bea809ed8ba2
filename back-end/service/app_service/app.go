package app_service

import (
	"fs-k8s-app-manager/models"
	"fs-k8s-app-manager/pkg/cache"
	"fs-k8s-app-manager/pkg/cache/key"
	"fs-k8s-app-manager/pkg/dto"
	"fs-k8s-app-manager/service/cmdb_service"
	"fs-k8s-app-manager/service/user_service"
	log "github.com/sirupsen/logrus"
	"strings"
	"time"
)

func FindAll() (ret []models.App, err error) {
	err = models.DB().Find(&ret).Error
	return
}
func Search(keyword string, level string, department string, page, limit int) (ret []models.App, err error) {
	db := models.DB().Model(&models.App{})
	if keyword != "" {
		db.Where("name LIKE ?", "%"+keyword+"%").
			Or("admins LIKE ?", "%"+keyword+"%").
			Or("remark LIKE ?", "%"+keyword+"%")
	}
	if level != "" {
		db.Where("level = ?", level)
	}
	if department != "" {
		db.Where("department = ?", department)
	}
	err = db.Offset((page - 1) * limit).Limit(limit).Order("id desc").Find(&ret).Error
	return
}
func Count(keyword string, level string, department string) int64 {
	var count int64
	db := models.DB().Model(&models.App{})
	if keyword != "" {
		db.Where("name LIKE ?", "%"+keyword+"%").
			Or("admins LIKE ?", "%"+keyword+"%").
			Or("remark LIKE ?", "%"+keyword+"%")
	}
	if level != "" {
		db.Where("level = ?", level)
	}
	if department != "" {
		db.Where("department = ?", department)
	}
	db.Count(&count)
	return count
}
func FindByName(name string) (ret models.App, err error) {
	err = models.DB().Where("name = ?", name).Take(&ret).Error
	return
}
func Exist(name string) bool {
	_, err := FindByName(name)
	return err == nil
}
func DeleteByName(name string) error {
	return models.DB().Unscoped().Where("name = ?", name).Delete(&models.App{}).Error
}

func Create(entity *models.App) (err error) {
	return models.DB().Create(entity).Error
}

func Update(entity *models.App) (err error) {
	return models.DB().Save(entity).Error
}

func GetAppOrgsWithCache(app string) []string {
	if app, found := GetAppsWithCache()[app]; found {
		return app.Orgs
	}
	return make([]string, 0)
}

func GetAppAllOwnersWithCache(app string) []string {
	if app, found := GetAppsWithCache()[app]; found {
		return app.GetAllOwners()
	}
	return make([]string, 0)
}

func GetAppMainOwnersWithCache(app string) []string {
	ret := make([]string, 0, 1)
	if app, found := GetAppsWithCache()[app]; found {
		ret = append(ret, app.MainOwner)
	}
	return ret
}

func ClearAppsCache() {
	cache.Delete(key.Pre().PERM.Key("app-cache"))
}
func GetAppsWithCache() map[string]models.App {
	var cacheKey = "app-cache"
	data := make(map[string]models.App)
	if v, found := cache.GetStruct(key.Pre().PERM.Key(cacheKey), data); found {
		data = v
	} else {
		if apps, err := FindAll(); err == nil {
			for _, app := range apps {
				data[app.Name] = app
			}
			_ = cache.SetStruct(key.Pre().PERM.Key(cacheKey), data, 1*time.Hour)
		} else {
			log.Error("create app org cache fail, " + err.Error())
		}
	}
	return data
}

func GetMainOwnerIdWithCache(app string) []int {
	ret := make([]int, 0, 1)
	items := GetAppMainOwnersWithCache(app)
	for _, it := range items {
		if u, err := user_service.FindByRealName(it); err == nil {
			ret = append(ret, u.EmployeeId)
		}
	}
	return ret
}

func SyncFromCMDB() (map[string]interface{}, error) {
	apps, err := FindAll()
	if err != nil {
		return nil, err
	}
	records, err := cmdb_service.GetDataFromCms()
	if err != nil {
		return nil, err
	}
	recordMap := make(map[string]dto.CmsCmdbRecord)
	for _, record := range records {
		recordMap[record.Service] = record
	}
	before := make([]models.App, 0, len(apps))
	after := make([]models.App, 0, len(apps))
	for _, app := range apps {
		appBefore := app
		record, found := recordMap[app.Name]
		if found {
			isDiff := false
			if app.Department != record.Departments {
				app.Department = record.Departments
				isDiff = true
			}
			if app.Level != record.Level {
				app.Level = record.Level
				isDiff = true
			}
			//if app.Remark != record.Info {
			//	app.Remark = record.Info
			//	isDiff = true
			//}
			if app.MainOwner != record.MainOwner {
				app.MainOwner = record.MainOwner
				isDiff = true
			}

			owners := make([]string, 0, 5)
			if record.Owner != "" {
				for _, o := range strings.Split(record.Owner, ",") {
					o = strings.TrimSpace(o)
					if o != "" {
						owners = append(owners, o)
					}
				}
			}
			if strings.Join(app.Owners, ",") != strings.Join(owners, ",") {
				app.Owners = owners
				isDiff = true
			}

			if isDiff {
				before = append(before, appBefore)
				after = append(after, app)
				_ = Update(&app)
			}
		}
	}
	ret := map[string]interface{}{
		"before": before,
		"after":  after,
	}
	return ret, nil
}
