package models

import (
	"fs-k8s-app-manager/models/datatype"
	"fs-k8s-app-manager/pkg/util/strslice"
	"gorm.io/gorm"
)

type App struct {
	gorm.Model
	Name       string               `gorm:"size:64;<-:create;index;unique;not null"`
	Remark     string               `gorm:"size:1024"`
	Department string               `gorm:"size:256"`
	Orgs       datatype.StrList     `gorm:"type:text;default:'[]'"`
	TimeWindow datatype.TimePeriods `gorm:"type:text;size:2048;default:'[]'"`
	Level      string               `gorm:"size:16"`
	MainOwner  string               `gorm:"size:256"`
	Owners     datatype.StrList     `gorm:"type:text;size:2048;default:'[]'"`
}

func (a *App) GetAllOwners() []string {
	ret := a.Owners
	if a.MainOwner != "" && !strslice.Find(ret, a.MainOwner) {
		ret = append(ret, a.MainOwner)
	}
	return ret
}
